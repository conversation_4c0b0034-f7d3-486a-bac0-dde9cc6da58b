# Copyright (c) 2022-2023, The lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import os

# import isaacsim.core.utils.nucleus as nucleus_utils
import torch

import isaaclab.sim as sim_utils
from isaaclab.assets import ArticulationCfg, AssetBaseCfg, RigidObject, RigidObjectCfg
from isaaclab.envs import ManagerBasedRLEnvCfg

# from isaaclab.managers import ObservationTermCfg as ObsTerm
from isaaclab.managers import CurriculumTermCfg as CurrTerm
from isaaclab.managers import EventTermCfg as EventTerm
from isaaclab.managers import ObservationGroupCfg as ObsGroup
from isaaclab.managers import RewardTermCfg as RewTerm
from isaaclab.managers import SceneEntityCfg
from isaaclab.managers import TerminationTermCfg as DoneTerm
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.sensors import ContactSensorCfg
from isaaclab.sim.spawners.from_files.from_files_cfg import GroundPlaneCfg, UsdFileCfg
from isaaclab.terrains import TerrainImporterCfg
from isaaclab.utils import configclass
from isaaclab.sensors import FrameTransformerCfg, OffsetCfg

import moleworks_ext.tasks.excavation.mdp as mdp
from moleworks_ext.common.actions.actions_cfg import InverseDynamicsActionCfg
from moleworks_ext.common.controllers.inverse_dynamics import InverseDynamicsControllerCfg
from moleworks_ext.common.env_cfg.general_env_cfg import transparent_plane
from moleworks_ext.common.managers.observations.obs_with_mean import ObservationWithMeanTermCfg as ObsTerm
from moleworks_ext.rsc.sim.m545 import M545_DOF_ARM_MERGED_CFG
from moleworks_ext.tasks.excavation.env_cfg.excavation_env_cfg import ExcavationEnvCfg
from moleworks_ext.tasks.excavation.env_cfg.excavation_env_cfg import ExcavationEnvCfg
from moleworks_ext.common.env_cfg.general_env_cfg import os1_lidar

##
# Scene definition
##
# Define common variables
ASSET_NAME = "robot"
EE_OFFSET = OffsetCfg(pos=(1.418, 0.0, 0.053), rot=(0.992567, 0.0, -0.121698, 0.0))

@configclass
class ExcavationSceneCfg(InteractiveSceneCfg):
    """Configuration for the terrain scene with a human robot."""
    # Handler for terrains importing
    terrain_importer_cfg = transparent_plane
    # sensors
    # camera = m545_camera
    lidar = None
    # robot
    print("ENV_REGEX_NS:", os.getenv("ENV_REGEX_NS"))
    robot: ArticulationCfg = M545_DOF_ARM_MERGED_CFG.replace(prim_path="{ENV_REGEX_NS}/Robot")

    # contact_forces = ContactSensorCfg(prim_path="{ENV_REGEX_NS}/Robot/.*")

    # Add FrameTransformerCfg for EE
    frame_transformer = FrameTransformerCfg(
        prim_path="{ENV_REGEX_NS}/Robot/BASE",
        target_frames=[
            FrameTransformerCfg.FrameCfg(
                name="EE",
                prim_path="{ENV_REGEX_NS}/Robot/ROTO_BASE",  # Replace with actual EE link path if different
                offset=EE_OFFSET
            ),
        ],
    )
    sky_light = AssetBaseCfg(
        prim_path="/World/skyLight",
        spawn=sim_utils.DomeLightCfg(color=(0.13, 0.13, 0.13), intensity=3000.0),
    )


@configclass
class CommandsCfg:
    """Command specifications for the MDP."""

    command = mdp.NullCommandCfg()  # No command is used for M545, specyfying it is needed


@configclass
class ActionsCfg:
    """Action specifications for the MDP."""
    inv_dyn_cfg = InverseDynamicsActionCfg(
                    asset_name="robot",
                    joint_names=["J_BOOM", "J_STICK", "J_TELE", "J_EE_PITCH"],
                    controller_cfg=InverseDynamicsControllerCfg(
                        command_type="vel",
                        k_p=[0, 0, 0, 0],
                        k_d=[25, 30, 20, 20],
                        dof_limits=[
                            [-0.5, 0.5],
                            [-0.6, 0.6],
                            [-0.4, 0.4],
                            [-0.8, 0.8]
                        ],
                        dof_efforts_limits=[
                            [-2e6, 2e6],
                            [-1e6, 1e6],
                            [-1e6, 1e6],
                            [-1e6, 1e6]
                        ]
                    )
                )


@configclass
class RewardsCfg:
    """Reward terms for the MDP."""
    bucket_velocity_penalty = RewTerm(func=mdp.reward_bucket_velocity_penalty, weight=0.01)
    # angle_of_attack_penalty = RewTerm(func=mdp.reward_angle_of_attack_penalty, weight=0.1)
    soil_spilling_penalty = RewTerm(func=mdp.reward_soil_spilling_penalty, weight=0.01)
    # RewTerm(func=mdp.action_rate_l2, weight=-1e-3)s
    action_rate = RewTerm(func=mdp.action_rate_l2_excavation, weight=-0.001)  #
    ## Constant reward
    constant = RewTerm(func=mdp.const_reward, weight=-0.001)  #
    # Soil dep reward
    bucket_filling = RewTerm(func=mdp.reward_bucket_filling, weight=0.5)  # 0.77
    max_depth_tracking = RewTerm(func=mdp.reward_max_depth_tracking, weight=0.01)
    bucket_curl = RewTerm(func=mdp.reward_bucket_curl, weight=0.02)  #
    pitch_up = RewTerm(func=mdp.reward_pitch_up, weight=0.02)  #
    bucket_edge_up = RewTerm(func=mdp.reward_bucket_edge_up, weight=0.0)  #
    bucket_edge_down = RewTerm(func=mdp.reward_bucket_edge_down, weight=-0.02)  #
    power = RewTerm(func=mdp.reward_power, weight=2e-7)  #
    bucket_curl_and_up = RewTerm(func=mdp.reward_bucket_curl_and_up, weight=0.0)  #
    # Termination conditions reward
    termination_reward = RewTerm(func=mdp.termination_reward, weight=0.2)



@configclass
class ObservationsCfg:
    """Observation specifications for the MDP."""

    @configclass
    class PolicyCfg(ObsGroup):
        """Observations for policy group."""

        # Joint Velocities, 4
        dof_pos = ObsTerm(func=mdp.dof_pos, params={"asset_cfg": SceneEntityCfg("robot", joint_names=[".*"])})
        # Joint positions, 4
        dof_vel = ObsTerm(
            func=mdp.dof_vel,
            params={"asset_cfg": SceneEntityCfg("robot", joint_names=[".*"])},
        )
        # Joint Torques, 4
        dof_tau = ObsTerm(
            func=mdp.dof_tau, mean=[-2.66e4, 2.45e4, 4.24e4, 1.19e4], divisor=[1.6e5, 1.44e5, 1.48e5, 5.47e4]
        )
        # previous action 4
        prev_action = ObsTerm(func=mdp.last_action_excavation)
        # Bucket lin gac, 4
        bucket_lin_gac = ObsTerm(func=mdp.bucket_lin_gac, mean=[6.0, -1.25, 0.0, 0.0], divisor=[2.0, 1.75, 1.0, 1.0])
        # Bucket ang gac, 1
        bucket_ang_gac = ObsTerm(func=mdp.bucket_ang_gac)
        ## Bucket lin_vel norm, 1
        bucket_lin_vel_norm = ObsTerm(func=mdp.bucket_lin_vel_norm)
        # Bucket Pitch Gac, 2
        base_pitch_gac = ObsTerm(func=mdp.base_pitch_gac, divisor=[0.1])
        # 1
        fill_ratio = ObsTerm(func=mdp.fill_ratio)
        # 1
        aoa = ObsTerm(func=mdp.aoa)
        # Enhanced spatial perception: 25 dimensions (5×5 grid around bucket)
        # 5 points across bucket width × 5 points ahead, 30cm spacing
        # Coverage: 1.2m × 1.2m area around bucket tip
        spatial_perception = ObsTerm(
            func=mdp.bucket_spatial_perception,
            mean=[-1.0] * 25,  # Expected soil height relative to base
            divisor=[2.0] * 25,  # Normalize by typical height range
        )
        # Bucket angle of attack: 1 dimension
        # Useful for both observation and potential termination condition
        bucket_aoa = ObsTerm(func=mdp.bucket_angle_of_attack_obs)
        # 1
        # bucket_depth = ObsTerm(func=mdp.bucket_depth, mean=[-0.25], divisor=[0.75])
        # 2
        # pitch_vel = ObsTerm(func=mdp.pitch_vel, divisor=[0.5, 0.5])
        # Enhanced spatial max depth: 25 dimensions (5×5 grid around bucket)
        # Uses same spatial approach as spatial_perception for consistency
        # 5 points across bucket width × 5 points ahead, 30cm spacing
        max_depth = ObsTerm(
            func=mdp.max_depth_spatial_perception,
            mean=[-1.25] * 25,  # Expected max depth relative to base
            divisor=[1.75] * 25,  # Normalize by typical depth range
        )
        # 1
        pullup_dist = ObsTerm(func=mdp.pullup_dist, mean=[2.75], divisor=[1.00])
        # 6
        # soil_parameters = ObsTerm(func=mdp.soil_parameM545_DOF_ARM_FULL_URDF_CFGters, mean = [50000.0, 0.5, 0.55, 19500.0, 0.285, 150.0], scale = [50000.0, 0.5, 0.25, 2500, 0.095, 150.0])

        def __post_init__(self):
            self.enable_corruption = True
            self.concatenate_terms = True

    # observation groups
    policy: PolicyCfg = PolicyCfg()


@configclass
class EventCfg:
    """Configuration for events."""

    # Reject sampling using a cache
    rejection_sampling = EventTerm(
        func=mdp.rejection_sampling,
        mode="reset",
    )


@configclass
class TerminationsCfg:
    """Termination terms for the MDP."""

    # NOTE: : Ternum_envsmination manager special for excavation is implemented because termination are checked differently
    # TODO: Change the manager to implement terminations here


@configclass
class CurriculumCfg:
    """Curriculum terms for the MDP."""

    # NOTE: Curriculum manager special for excavation is implemented because curriculum buffers need
    #       to be updated over iterations


@configclass
class Limits:
    @configclass
    class Velocity:
        upper = [0.3, 0.6, 0.4, 0.8]
        lower = [-0.3, -0.6, -0.4, -0.8]

    velocity = Velocity()

    @configclass
    class Position:
        upper = [0.4, 2.76, 1.8, 2.32]
        lower = [-1.29, 0.54, 0.0, -0.59]

    position = Position()

    # from m545_description/m545_cylinder_definitions.hpp
    @configclass
    class CylinderForce:
        upper = [530100.0, 398100.0, 190800.0, 235500.0]
        lower = [-379180.53, -282611.19, -105894.0, -150908.4]

    cylinder_force = CylinderForce()

    @configclass
    class CylinderVelocity:
        upper = [0.118, 0.308, 0.416, 0.334]
        lower = [-0.132, -0.295, -0.381, -0.33]

    cylinder_velocity = CylinderVelocity()


@configclass
class M545EnvCfg(ExcavationEnvCfg):
    """Configuration for the locomotion velocity-tracking environment."""

    # Scene settings
    scene: ExcavationSceneCfg = ExcavationSceneCfg(num_envs=16000, env_spacing=15)  # 15
    # Basic settings
    observations: ObservationsCfg = ObservationsCfg()
    actions: ActionsCfg = ActionsCfg()
    commands: CommandsCfg = CommandsCfg()
    # MDP settings
    rewards: RewardsCfg = RewardsCfg()
    terminations: TerminationsCfg = TerminationsCfg()
    events: EventCfg = EventCfg()
    curriculum: CurriculumCfg = CurriculumCfg()

    # gains PID Tuning
    k_d = [25, 30, 20, 20]
    arm_joints_names = ["J_BOOM", "J_STICK", "J_TELE", "J_EE_PITCH"]
    ee_body_name = "ROTO_BASE"

    limits = Limits()
    enable_lidar = False

    def __post_init__(self):
        """Post initialization."""
        if self.enable_lidar:
            self.scene.lidar = os1_lidar
        # general settings
        self.decimation = 4
        self.episode_length_s = 19.95
        # simulation settings
        self.sim.dt = 0.04
        # self.sim.disable_contact_processing = True
        self.sim.physx.bounce_threshold_velocity = 1.0
        self.sim.physx.solver_type = 1.0
        self.sim.render_interval = 10
        self.sim.physx.gpu_max_rigid_patch_count = 100000 * 4

        # Memory optimization for multi-GPU training
        import os
        num_gpus = int(os.environ.get("WORLD_SIZE", 1))
        if num_gpus > 1:
            # Enable memory optimizations for multi-GPU training
            self.soil_model_cfg.disable_cudagraphs = True
            self.soil_model_cfg.compilation_mode = "default"
            print(f"[INFO] Multi-GPU training detected ({num_gpus} GPUs). Enabling memory optimizations.")
