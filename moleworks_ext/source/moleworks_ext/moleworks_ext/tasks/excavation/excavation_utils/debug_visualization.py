# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Debug visualization utilities for the excavation environment."""

from __future__ import annotations

import torch
import numpy as np
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from moleworks_ext.tasks.excavation.excavation_env_3d import ExcavationEnv


class DebugVisualization:
    """Debug visualization system for excavation environment.
    
    This class provides visualization of the 5x5 spatial perception grid observations
    that the RL agent receives, making it easier to debug and understand the observation space.
    """

    def __init__(self, env: "ExcavationEnv"):
        """Initialize the debug visualization system.
        
        Args:
            env: The excavation environment instance
        """
        self.env = env
        self.device = env.device
        
        # Configuration from environment
        self.enable_debug_visualization = getattr(
            env.cfg.soil_model_cfg, 'enable_debug_visualization', False
        )
        self.debug_update_steps = getattr(
            env.cfg.soil_model_cfg, 'debug_visualization_update_steps', 20
        )
        
        # Tracking variables
        self.debug_update_counter = 0
        self.last_grid_positions = None
        self.last_grid_heights = None
        
        # Grid configuration (matches spatial perception observation)
        self.points_width = 5  # Points across bucket width
        self.points_forward = 5  # Points ahead of bucket
        self.spacing = 0.3  # 30cm spacing between points
        
        # Visualization colors
        self.grid_point_color = [0.0, 1.0, 1.0, 1.0]  # Cyan for grid points
        self.bucket_color = [1.0, 0.0, 0.0, 1.0]  # Red for bucket position
        self.height_line_color = [0.0, 1.0, 0.0, 1.0]  # Green for height lines
        
        # Initialize drawing interface if GUI is available
        self.draw_interface = None
        if env.sim.has_gui():
            try:
                import isaacsim.util.debug_draw._debug_draw as omni_debug_draw
                self.draw_interface = omni_debug_draw.acquire_debug_draw_interface()
            except ImportError:
                print("[WARNING] Could not import debug draw interface for debug visualization")

    def should_update(self) -> bool:
        """Check if debug visualization should be updated.
        
        Returns:
            True if visualization should be updated, False otherwise
        """
        if not self.enable_debug_visualization:
            return False
            
        if not self.env.sim.has_gui():
            return False
            
        if self.env.train:  # Skip during training mode
            return False
            
        if self.draw_interface is None:
            return False
            
        # Update based on step counter
        self.debug_update_counter += 1
        if self.debug_update_counter >= self.debug_update_steps:
            self.debug_update_counter = 0
            return True
            
        return False

    def update(self):
        """Update the debug visualization.
        
        This method visualizes the 5x5 spatial perception grid that the RL agent observes.
        """
        if not self.should_update():
            return
            
        try:
            # Get current observations
            if not hasattr(self.env, 'observation_manager'):
                return

            obs_dict = self.env.observation_manager.compute_group("policy")

            # Check if spatial_perception observation exists
            if "spatial_perception" not in obs_dict:
                return

            spatial_obs = obs_dict["spatial_perception"]
            
            # Only visualize for environment 0 to avoid clutter
            env_id = 0
            if spatial_obs.shape[0] <= env_id:
                return
                
            # Get spatial perception for environment 0
            grid_heights = spatial_obs[env_id]  # [25] values
            
            if grid_heights.shape[0] != 25:
                print(f"[WARNING] Expected 25 spatial perception values, got {grid_heights.shape[0]}")
                return
                
            # Compute grid positions in world coordinates
            grid_positions = self._compute_grid_positions(env_id)
            
            # Clear previous debug drawings
            if hasattr(self.draw_interface, 'clear_points'):
                self.draw_interface.clear_points()
            if hasattr(self.draw_interface, 'clear_lines'):
                self.draw_interface.clear_lines()
                
            # Draw the spatial perception grid
            self._draw_spatial_grid(grid_positions, grid_heights, env_id)
            
            # Store for next update
            self.last_grid_positions = grid_positions
            self.last_grid_heights = grid_heights
            
        except Exception as e:
            print(f"[ERROR] Debug visualization update failed: {e}")

    def _compute_grid_positions(self, env_id: int) -> torch.Tensor:
        """Compute the world positions of the 5x5 spatial perception grid.

        Args:
            env_id: Environment ID to compute positions for

        Returns:
            Grid positions in world coordinates [25, 3]
        """
        # Check if measurements are available
        if not hasattr(self.env, 'm545_measurements'):
            return torch.zeros((25, 3), device=self.device)

        # Get bucket position in world frame
        bucket_pos_w = self.env.m545_measurements.bucket_pos_w[env_id]  # [3]
        
        # Calculate bucket-relative sampling points (same as in spatial perception observation)
        width_extent = (self.points_width - 1) * self.spacing / 2  # ±0.6m from bucket center
        width_offsets = torch.linspace(-width_extent, width_extent, self.points_width, device=self.device)
        
        # Forward: starting from bucket tip, going forward
        forward_offsets = torch.arange(0, self.points_forward, device=self.device) * self.spacing
        
        # Create sampling grid in bucket-relative coordinates
        x_grid, y_grid = torch.meshgrid(forward_offsets, width_offsets, indexing='ij')
        
        # Flatten for processing
        x_flat = x_grid.flatten()  # [25] - forward distances
        y_flat = y_grid.flatten()  # [25] - lateral distances
        
        # Transform to world coordinates
        grid_positions = torch.zeros((25, 3), device=self.device)
        grid_positions[:, 0] = bucket_pos_w[0] + x_flat  # X coordinates
        grid_positions[:, 1] = bucket_pos_w[1] + y_flat  # Y coordinates
        grid_positions[:, 2] = bucket_pos_w[2]  # Z coordinate (will be adjusted with soil heights)
        
        return grid_positions

    def _draw_spatial_grid(self, grid_positions: torch.Tensor, grid_heights: torch.Tensor, env_id: int):
        """Draw the 5x5 spatial perception grid.
        
        Args:
            grid_positions: Grid positions in world coordinates [25, 3]
            grid_heights: Soil heights at grid positions [25]
            env_id: Environment ID
        """
        # Get environment origin for proper positioning
        env_origin = self.env.scene.env_origins[env_id]
        
        # Convert to base frame heights (add robot base height)
        base_height = self.env.m545_measurements.root_pos_w[env_id, 2]
        world_heights = grid_heights + base_height
        
        # Update grid positions with actual soil heights
        grid_positions_with_heights = grid_positions.clone()
        grid_positions_with_heights[:, 2] = world_heights
        
        # Add environment origin offset
        grid_positions_final = grid_positions_with_heights + env_origin
        
        # Draw grid points
        self._draw_grid_points(grid_positions_final, grid_heights)
        
        # Draw height indicators
        self._draw_height_indicators(grid_positions_final, env_origin, env_id)
        
        # Draw bucket position for reference
        self._draw_bucket_reference(env_id)

    def _draw_grid_points(self, positions: torch.Tensor, heights: torch.Tensor):
        """Draw the grid points with color coding based on height values.
        
        Args:
            positions: Grid positions [25, 3]
            heights: Height values for color coding [25]
        """
        if not hasattr(self.draw_interface, 'draw_points'):
            return
            
        # Convert to list format for drawing
        points = positions.cpu().numpy().tolist()
        
        # Create colors based on height values (normalize to [0, 1] range)
        height_min = heights.min().item()
        height_max = heights.max().item()
        height_range = max(height_max - height_min, 0.1)  # Avoid division by zero
        
        colors = []
        for height in heights:
            # Normalize height to [0, 1]
            normalized_height = (height.item() - height_min) / height_range
            # Color from blue (low) to red (high)
            color = [normalized_height, 0.0, 1.0 - normalized_height, 1.0]
            colors.append(color)
        
        # Draw points
        sizes = [0.05] * len(points)  # 5cm radius points
        self.draw_interface.draw_points(points, colors, sizes)

    def _draw_height_indicators(self, positions: torch.Tensor, env_origin: torch.Tensor, env_id: int):
        """Draw vertical lines indicating soil heights.

        Args:
            positions: Grid positions [25, 3]
            env_origin: Environment origin [3]
            env_id: Environment ID
        """
        if not hasattr(self.draw_interface, 'draw_lines'):
            return

        if not hasattr(self.env, 'm545_measurements'):
            return

        # Get bucket height for reference
        bucket_height = self.env.m545_measurements.bucket_pos_w[env_id, 2] + env_origin[2]
        
        # Draw vertical lines from bucket height to soil height
        line_starts = []
        line_ends = []
        
        for i, pos in enumerate(positions):
            start_pos = [pos[0].item(), pos[1].item(), bucket_height.item()]
            end_pos = [pos[0].item(), pos[1].item(), pos[2].item()]
            
            line_starts.append(start_pos)
            line_ends.append(end_pos)
        
        # Draw all lines
        colors = [self.height_line_color] * len(line_starts)
        thicknesses = [2.0] * len(line_starts)
        
        self.draw_interface.draw_lines(line_starts, line_ends, colors, thicknesses)

    def _draw_bucket_reference(self, env_id: int):
        """Draw bucket position and orientation for reference.

        Args:
            env_id: Environment ID
        """
        if not hasattr(self.draw_interface, 'draw_points'):
            return

        if not hasattr(self.env, 'm545_measurements') or not hasattr(self.env, 'scene'):
            return

        # Get bucket position
        bucket_pos = self.env.m545_measurements.bucket_pos_w[env_id]
        env_origin = self.env.scene.env_origins[env_id]
        bucket_world_pos = bucket_pos + env_origin
        
        # Draw bucket center point
        bucket_point = [bucket_world_pos.cpu().numpy().tolist()]
        bucket_color = [self.bucket_color]
        bucket_size = [0.1]  # 10cm radius
        
        self.draw_interface.draw_points(bucket_point, bucket_color, bucket_size)

    def reset(self):
        """Reset the debug visualization state."""
        self.debug_update_counter = 0
        self.last_grid_positions = None
        self.last_grid_heights = None

        # Clear any existing debug drawings
        if self.draw_interface is not None:
            try:
                if hasattr(self.draw_interface, 'clear_points'):
                    self.draw_interface.clear_points()
                if hasattr(self.draw_interface, 'clear_lines'):
                    self.draw_interface.clear_lines()
            except Exception as e:
                print(f"[WARNING] Failed to clear debug drawings: {e}")
