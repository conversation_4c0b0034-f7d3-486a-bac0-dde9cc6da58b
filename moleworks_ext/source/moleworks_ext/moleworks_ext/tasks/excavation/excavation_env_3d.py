# Copyright (c) 2022-2023, The lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import numpy as np
import torch
from typing import Any, ClassVar, Sequence

from isaacsim.core.version import get_version


from isaaclab.envs.manager_based_rl_env import ManagerBasedRLEnv
from .env_cfg.excavation_env_cfg import ExcavationEnvCfg

from moleworks_ext.common.utils.m545_measurements import M545Measurements
from .excavation_utils.termination_excavation import Terminations_Excavation
from .excavation_utils.curriculum_excavation import Curriculum_Excavation
from .excavation_utils.reset_cache import ResetCache
from .excavation_utils.limits import Limits

from isaaclab.envs import VecEnvStepReturn

from .soil_model_factory import create_soil_model

from moleworks_ext.tasks.excavation.excavation_utils.excavation_reward_manager import (
    ExcavationRewardManager,
)
from moleworks_ext.tasks.excavation.excavation_utils.debug_visualization import (
    DebugVisualization,
)
from isaaclab.managers.action_manager import ActionManager
from moleworks_ext.common.managers.observations.obs_with_mean import (
    ObservationManagerWithMean,
)


class ExcavationEnv(ManagerBasedRLEnv):
    """The class for reinforcement learning-based environments for excavation.

    This class inherits from :class:`RLTaskEnv` and implements the core functionality for
    reinforcement learning-based environments for an excavation agent. It is designed to be used with any RL
    library. This class differs from RLTaskEnv in the sense it has a pre-physics and post physics step.
    Additional measurements buffers concerning are updated and a custom soil model is used.

    Note:
        For vectorized environments, it is recommended to **only** call the :meth:`reset`
        method once before the first call to :meth:`step`, i.e. after the environment is created.
        After that, the :meth:`step` function handles the reset of terminated sub-environments.
        This is because the simulator does not support resetting individual sub-environments
        in a vectorized environment.
    """

    is_vector_env: ClassVar[bool] = True
    """Whether the environment is a vectorized environment."""
    metadata: ClassVar[dict[str, Any]] = {
        "render_modes": [None, "human", "rgb_array"],
        "isaac_sim_version": get_version(),
    }
    """Metadata for the environment."""

    cfg: ExcavationEnvCfg
    """Configuration for the environment."""

    def __init__(self, cfg: ExcavationEnvCfg, render_mode: str | None = None, **kwargs):
        """self.m545_measurements = M545Measurements(
            num_joints=len(cfg.scene.robot.init_state.joint_pos), num_envs=cfg.scene.num_envs, device=cfg.sim.device
        )"""
        self.m545_measurements = M545Measurements(
            cfg=cfg,
            num_joints=len(cfg.scene.robot.init_state.joint_pos),
            num_envs=cfg.scene.num_envs,
            device=cfg.sim.device,
            env=self,
        )
        # Environment buffers
        self.init_env_buffers(
            num_envs=cfg.scene.num_envs,
            num_joints=len(cfg.arm_joints_names),
            device=cfg.sim.device,
            cfg=cfg,
        )
        # Soil - use factory to create appropriate model
        self.soil = create_soil_model(cfg)

        # Visualization tracking
        self.soil_visualization_initialized = False
        self.needs_full_mesh_update = False

        # Step-based soil height update tracking for play_3d mode
        self.soil_height_update_counter = 0
        self.soil_height_update_steps = getattr(cfg.soil_model_cfg, 'soil_height_update_steps', 10)

        # Training vs play mode detection
        self.train = True  # Will be set to False in play scripts (used by play scripts)

        # it initializes all the managers, needs to be after init_env_buffers
        super().__init__(cfg)
        # needs to be after super().__init__(cfg)
        self.m545_asset = self.scene.articulations["robot"]
        self.m545_measurements.initialize_asset()
        # load a different reward manager
        self.reward_manager = ExcavationRewardManager(self.cfg.rewards, self)
        self.action_manager = ActionManager(self.cfg.actions, self)
        self.observation_manager = ObservationManagerWithMean(
            self.cfg.observations, self
        )
        # Post init now that we have access to the asset and the env
        self.soil.post_init(self)
        # self.m545_measurements.post_init(self)
        # Update Static measuremetns
        self.joint_ids, self._joint_names = self.m545_asset.find_joints([".*"])

        self.reset_cache = ResetCache(self)
        self.limits = Limits(self)
        # Custom managers
        self.termination_excavation = Terminations_Excavation(self)
        self.curriculum_excavation = Curriculum_Excavation(self)

        # Initialize debug visualization system (after sim is available)
        self.debug_visualization = DebugVisualization(self)

        # Initialize the markers and drawers needed for the visualization
        if self.sim.has_gui():
            # Import packages. Done here since omni_debug_draw cannot be imported if headless
            import isaacsim.util.debug_draw._debug_draw as omni_debug_draw
            from .excavation_utils.visualization_excavation import define_markers

            # Drawing tools
            self.excavation_visualizer = define_markers()
            self.excavation_draw_interface = (
                omni_debug_draw.acquire_debug_draw_interface()
            )

        self.reset()

    def _debug_print_observations(self):
        """Debug function to print observation details for environment 0."""
        try:
            # Get observations for environment 0
            obs_dict = self.observation_manager.compute_group("policy")
            env_id = 0

            print(f"\n{'='*60}")
            print(f"OBSERVATION DEBUG - Step {self.common_step_counter} - Env {env_id}")
            print(f"{'='*60}")

            # Print each observation term
            total_dims = 0
            for obs_name, obs_tensor in obs_dict.items():
                if obs_tensor.dim() > 1:
                    obs_value = obs_tensor[env_id]
                    dims = obs_value.shape[0] if obs_value.dim() > 0 else 1
                else:
                    obs_value = obs_tensor
                    dims = 1

                total_dims += dims

                print(f"\n{obs_name:20s} ({dims:2d} dims): ", end="")

                # Special handling for different observation types
                if obs_name == "spatial_perception":
                    # Reshape spatial perception to 5x5 grid for better visualization
                    if dims == 25:
                        grid = obs_value.view(5, 5)
                        print(f"5x5 Grid (bucket-centered):")
                        print(f"  Range: [{obs_value.min().item():.3f}, {obs_value.max().item():.3f}]")
                        print(f"  Mean: {obs_value.mean().item():.3f}")
                        print(f"  Grid layout (forward=rows, width=cols):")
                        for i in range(5):
                            print(f"    {i*0.3:.1f}m: ", end="")
                            for j in range(5):
                                print(f"{grid[i,j].item():6.2f} ", end="")
                            print()
                    else:
                        print(f"Shape: {obs_value.shape}, Values: {obs_value.tolist()}")

                elif obs_name in ["soil_height", "max_depth"]:
                    # Legacy 1D perception
                    print(f"1D Line (0.2m spacing):")
                    print(f"  Range: [{obs_value.min().item():.3f}, {obs_value.max().item():.3f}]")
                    print(f"  Values: ", end="")
                    for i, val in enumerate(obs_value):
                        print(f"{i*0.2:.1f}m:{val.item():6.2f} ", end="")
                    print()

                elif obs_name in ["dof_pos", "dof_vel", "dof_tau", "prev_action"]:
                    # Joint-related observations
                    joint_names = ["boom", "stick", "tele", "pitch"]
                    print(f"Joints: ", end="")
                    for i, (name, val) in enumerate(zip(joint_names, obs_value)):
                        print(f"{name}:{val.item():7.3f} ", end="")
                    print()

                elif dims <= 4:
                    # Small observations - print all values
                    if dims == 1:
                        print(f"{obs_value.item():.4f}")
                    else:
                        values_str = " ".join([f"{v.item():.3f}" for v in obs_value])
                        print(f"[{values_str}]")
                else:
                    # Large observations - print summary
                    print(f"Range: [{obs_value.min().item():.3f}, {obs_value.max().item():.3f}], Mean: {obs_value.mean().item():.3f}")

            print(f"\n{'='*60}")
            print(f"TOTAL OBSERVATION DIMENSIONS: {total_dims}")
            print(f"CONCATENATED OBSERVATION SHAPE: {self.obs_buf['policy'][env_id].shape}")
            print(f"{'='*60}\n")

        except Exception as e:
            print(f"Error in observation debug: {e}")

    def inter_decim_step(self) -> None:
        """
        Executes one inter decimation step. This is used for debugging and not for training.
        """
        # Pre-physics step
        self.pre_physics_step()
        # set actions into buffers, both are doing the same, one is lab fashion and other Gym fashion
        self.action_manager.apply_action()
        # set actions into simulator
        self.scene.write_data_to_sim()
        # simulate
        self.sim.step(render=False)
        # Post-physics step
        self.post_physics_step()

    def step(self, action: torch.Tensor) -> VecEnvStepReturn:
        """Execute one time-step of the environment's dynamics and reset terminated environments.

        Unlike the :class:`RL_Task:Env.step` class, the function performs the following operations:

        0. Pr-Physics step
        1. Process the actions.
        2. Perform physics stepping.
        3. Perform rendering if gui is enabled.
        4. Update the environment counters and compute the rewards and terminations.
        5. Reset the environments that terminated.
        6. Compute the observations.
        7. Return the observations, rewards, resets and extras.

        Args:
            action: The actions to apply on the environment. Shape is (num_envs, action_dim).

        Returns:
            A tuple containing the observations, rewards, resets (terminated and truncated) and extras.
        """
        # Process actions
        self.actions[:] = action  # Logging
        self.action_manager.process_action(action)
        # Pre-decimation. Soil has been updated in initial reset()
        self.last_fill_ratio[:] = self.soil.get_fill_ratio()
        self.inter_decimation_soil_model_invalid[:] = False
        self.inter_decimation_self_collision[:] = False

        # check if we need to do rendering within the physics loop
        # note: checked here once to avoid multiple checks within the loop
        is_rendering = self.sim.has_gui() or self.sim.has_rtx_sensors()
        # perform physics stepping
        for _ in range(self.cfg.decimation):
            # Pre-physics step
            self.pre_physics_step()
            # set actions into buffers,
            self.action_manager.apply_action()
            # set actions into simulator
            self.scene.write_data_to_sim()
            # simulate
            self.sim.step(render=False)
            self._sim_step_counter += 1
            # render between steps only if the GUI or an RTX sensor needs it
            # note: we assume the render interval to be the shortest accepted rendering interval.
            #    If a camera needs rendering at a faster frequency, this will lead to unexpected behavior.
            if (
                self._sim_step_counter % self.cfg.sim.render_interval == 0
                and is_rendering
            ):
                self.draw_debug_vis()
                self.sim.render()
            # Post-physics step
            self.post_physics_step()
        # Measurements updates for the one that are not needed in inter steps
        self.update_derived_measurements()

        # post-step:
        # -- update env counters (used for curriculum generation)
        self.episode_length_buf += 1  # step in current episode (per env)
        self.common_step_counter += 1  # total step (common for all envs)
        # -- check terminations
        self.reset_buf = self.termination_excavation.check_termination()
        self.reset_terminated = self.termination_excavation.terminated
        self.reset_time_outs = self.termination_excavation.time_out_buf

        # Check for successful digging and update visualization
        positive_term = self.termination_excavation.full_pos_term_buf
        self.extras["positive_term"] = positive_term
        self.extras["termination_reason"] = "negative"
        if torch.any(positive_term.bool()):
            self.needs_full_mesh_update = True
            # Add termination reason to extras
            self.extras["termination_reason"] = "positive"

        # Print termination cause for environment 0
        if self.reset_buf[0]:
            # Determine termination cause for env 0
            term_cause = "Unknown"
            if self.termination_excavation.time_out_buf[0]:
                term_cause = "Timeout"
            elif self.termination_excavation.full_pos_term_buf[0]:
                term_cause = "Positive: Full bucket"
            elif self.termination_excavation.close_pos_term_buf[0]:
                term_cause = "Positive: Close to target"
            elif self.termination_excavation.partial_pos_term_buf[0]:
                term_cause = "Positive: Partial fill"
            elif self.termination_excavation.neg_term_buf[0]:
                # Check which negative termination was triggered
                for name in self.termination_excavation.neg_term_names:
                    if self.termination_excavation.episode_neg_term_buf[name][0]:
                        term_cause = f"Negative: {name}"
                        break

            print(f"\n=== ENV 0 TERMINATED: {term_cause} ===")
            print(f"Fill ratio: {self.soil.get_fill_ratio()[0].item():.4f}")

        # -- reward computation
        self.reward_buf = self.reward_manager.compute(dt=self.step_dt)

        # -- reset envs that terminated/timed-out and log the episode information
        reset_env_ids = self.reset_buf.nonzero(as_tuple=False).squeeze(-1)
        # Invalid soils of env before reset
        not_reset_env_ids = (~self.reset_buf).nonzero(as_tuple=False).flatten()
        invalid_not_resetting_before = self.soil.is_state_invalid(not_reset_env_ids)

        # -- reset envs that terminated/timed-out and log the episode information
        if len(reset_env_ids) > 0:
            self._reset_idx(reset_env_ids)
            # if sensors are added to the scene, make sure we render to reflect changes in reset
            if self.sim.has_rtx_sensors() and self.cfg.rerender_on_reset:
                self.sim.render()
        # Check if episode_length_buf are fine
        if torch.any(self.episode_length_buf > self.common_step_counter):
            print(
                "An element in episode_length_buf is greater than common_step_counter"
            )
        # NOTE: Command manager not used
        # Invalid soils of env after reset
        invalid_not_resetting_after = self.soil.is_state_invalid(not_reset_env_ids)
        if (invalid_not_resetting_before != invalid_not_resetting_after).any():
            raise ValueError(print("reset of others made not resetted invalid"))
        # -- step interval randomization, not used in Excavation v0
        if "interval" in self.event_manager.available_modes:
            self.event_manager.apply(mode="interval", dt=self.step_dt)
        # Update derived measurements for resetted envs
        self.update_derived_measurements(reset_env_ids)
        # -- compute observations
        # note: done after reset to get the correct observations for reset envs
        self.obs_buf = self.observation_manager.compute()

        # Debug: Print observation details every 100 steps for environment 0
        if self.common_step_counter % 100 == 0:
            self._debug_print_observations()

        # Not used by lab reward
        self.last_actions = self.actions[:]
        self.last_actions[reset_env_ids] = 0

        # return observations, rewards, resets and extras
        return (
            self.obs_buf,
            self.reward_buf,
            self.reset_terminated,
            self.reset_time_outs,
            self.extras,
        )

    def pre_physics_step(self):
        """
        Update buffers before physics step
        """
        self.set_soil_forces()

    def post_physics_step(self):
        """
        Update buffers after physics step
        """
        # Update buffers at sim dt
        self.scene.update(dt=self.physics_dt)
        # Update measurements
        self.m545_measurements.update_measurements()
        # Update the soil
        self.soil.update()

        # Step-based soil height updates for play_3d mode
        self.soil_height_update_counter += 1
        if (not self.train and
            self.soil_height_update_counter >= self.soil_height_update_steps):
            # Reset counter and trigger mesh update for visualization
            self.soil_height_update_counter = 0
            if hasattr(self, "needs_full_mesh_update"):
                self.needs_full_mesh_update = True

        # Track steps since last mesh update for visualization timing
        if hasattr(self, "steps_since_last_update"):
            self.steps_since_last_update += 1

        # Update debug visualization for spatial perception grid
        if hasattr(self, "debug_visualization"):
            self.debug_visualization.update()

        # Check is the soil is valid
        self.inter_decimation_soil_model_invalid |= self.soil.is_state_invalid()
        # # Check collisions
        # self.inter_decimation_self_collision |= (
        #    torch.linalg.norm(self.m545_measurements.bucket_collision_f, dim=-1, keepdim=True) > 1.0
        # )

    def update_derived_measurements(self, env_ids=...):
        """
        Updates measurements that are not needed in inter-decimations step but relevant
        for rewards, curriculum, terminations and observations computations
        """
        if env_ids == ...:
            dim0 = self.num_envs
        else:
            dim0 = len(env_ids)

        if dim0 == 0:
            return

        self.m545_measurements.update_derived_measurements(env_ids, dim0)

        # For 3D soil models, we need to pass both x and y coordinates
        # moving in negative x direction: x, x-d, x-2d
        bucket_pos_xy = self.m545_measurements.bucket_pos_w[
            env_ids, 0:2
        ]  # Get both x and y

        # Create soil future query points (moving in negative x direction)
        # Use the bucket's y-position for all queries
        x_points = bucket_pos_xy[:, 0:1] - self.soil_height_futures_spacing
        y_points = torch.tile(
            bucket_pos_xy[:, 1:2], (1, self.soil_height_futures_spacing.shape[0])
        )
        self.soil_height_futures[env_ids] = self.soil.get_soil_height_at_pos(
            x_points, y_points, env_ids
        )

        # Max depth futures
        x_points = bucket_pos_xy[:, 0:1] - self.max_depth_futures_spacing
        y_points = torch.tile(
            bucket_pos_xy[:, 1:2], (1, self.max_depth_futures_spacing.shape[0])
        )
        self.max_depth_futures[env_ids] = self.soil.get_max_depth_height_at_pos(
            x_points, y_points, env_ids
        )

        # For soil normal futures
        x_points = bucket_pos_xy[:, 0:1] - self.soil_normal_futures_spacing
        y_points = torch.tile(
            bucket_pos_xy[:, 1:2], (1, self.soil_normal_futures_spacing.shape[0])
        )
        # For now just get the xz angle
        self.soil_normal_futures[env_ids] = self.soil.get_soil_angle_at_pos(
            x_points, y_points, env_ids
        )[0]

        # For 3D soil, get the current soil angle at the bucket position

        soil_angles = self.soil.get_soil_angle_at_pos(
            bucket_pos_xy[:, 0:1], bucket_pos_xy[:, 1:2], env_ids
        )
        angle_x, angle_y = soil_angles
        angle_x = angle_x.squeeze()
        angle_y = angle_y.squeeze()

        # Compute normal vector components (x, y, z) from the two angles
        self.soil_normal_vec[env_ids, 0] = torch.cos(angle_x + np.pi / 2.0)
        self.soil_normal_vec[env_ids, 1] = torch.cos(angle_y + np.pi / 2.0)
        self.soil_normal_vec[env_ids, 2] = torch.sin(angle_x + np.pi / 2.0)

        # Normalize the vector
        norm = torch.norm(self.soil_normal_vec[env_ids], dim=1, keepdim=True)
        self.soil_normal_vec[env_ids] = self.soil_normal_vec[env_ids] / (norm + 1e-8)

    def init_env_buffers(self, num_envs, num_joints, device, cfg):
        """
        Initializes env buffers
        """

        # super constructor from baseenv
        self.reset_buf = torch.ones(num_envs, device=device, dtype=torch.long)

        self.max_depth_futures = torch.zeros(
            num_envs, cfg.observations_excavation.num_max_depth_futures, device=device
        )
        self.max_depth_futures_spacing = (
            torch.arange(
                0, cfg.observations_excavation.num_max_depth_futures, device=device
            )
            * cfg.observations_excavation.max_depth_futures_spacing
        )
        self.soil_height_futures = torch.zeros(
            num_envs, cfg.observations_excavation.num_soil_height_futures, device=device
        )
        self.soil_height_futures_spacing = (
            torch.arange(
                0, cfg.observations_excavation.num_soil_height_futures, device=device
            )
            * cfg.observations_excavation.soil_height_futures_spacing
        )

        self.soil_normal_futures = torch.zeros(
            num_envs, cfg.observations_excavation.num_soil_normal_futures, device=device
        )
        self.soil_normal_futures_spacing = (
            torch.arange(
                0, cfg.observations_excavation.num_soil_normal_futures, device=device
            )
            * cfg.observations_excavation.max_soil_normal_futures_spacing
        )

        self.soil_normal_vec = torch.zeros(num_envs, 3, device=device)

        # Reset/ Termination
        self.pullup_dist = torch.zeros(num_envs, device=device)
        self.pullup_dist[:] = cfg.reset.pullup_dist
        self.inter_decimation_soil_model_invalid = torch.zeros(
            num_envs, 1, dtype=torch.bool, device=device
        )
        self.inter_decimation_self_collision = torch.zeros(
            num_envs, 1, dtype=torch.bool, device=device
        )

        # m545
        # self.m545_num_rb = len(self.m545_asset.body_names)
        self.vel_limits_lower = torch.tensor(cfg.limits.velocity.lower, device=device)
        self.vel_limits_upper = torch.tensor(cfg.limits.velocity.upper, device=device)
        # this is wrong
        # self.external_force = torch.zeros(num_envs, self.m545_num_rb, 3, device=device)
        # self.bucket_force_com = self.external_force[:, self.m545_measurements.bucket_body_idx, :]
        # self.external_moment = torch.zeros(num_envs, self.m545_num_rb, 3, device=device)
        # self.bucket_moment_com = self.external_moment[:, self.m545_measurements.bucket_body_idx, :]
        self.bucket_force_com = torch.zeros(num_envs, 3, device=device)
        self.bucket_moment_com = torch.zeros(num_envs, 3, device=device)

        self.actions = torch.zeros(num_envs, num_joints, device=device)
        self.des_dof_vel = torch.zeros(num_envs, num_joints, device=device)
        self.des_dof_pos = torch.zeros(num_envs, num_joints, device=device)
        # todo: torques should be updated here
        self.torques = torch.zeros(num_envs, num_joints, device=device)
        self.inertial_tau = torch.zeros(num_envs, num_joints, device=device)
        self.ext_f_tau = torch.zeros(num_envs, num_joints, device=device)  # soil
        self.ext_m_tau = torch.zeros(num_envs, num_joints, device=device)

        # RL
        self.actions = torch.zeros(num_envs, num_joints, device=device)
        self.last_actions = torch.zeros(num_envs, num_joints, device=device)
        self.last_fill_ratio = torch.zeros(num_envs, 1, device=device)
        self.clipped_scaled_actions = torch.zeros(num_envs, num_joints, device=device)
        # Helpers
        self.zero_scalar = torch.zeros(1, device=device)
        self.zero_vec = torch.zeros(num_envs, 1, device=device)
        # Log indices from reset cache of all envs
        self.sampled = torch.zeros(num_envs, device=device).long()
        self.des_dof_vel = torch.zeros(num_envs, num_joints, device=device)
        self.des_dof_pos = torch.zeros(num_envs, num_joints, device=device)

    def set_soil_forces(self):
        """
        Get forces from the soil model and apply them to the bucket.
        This function handles both 2D and 3D soil models.
        """
        # Get forces and moments from soil model
        self.bucket_force_com[:] = self.soil.get_resultant_force()
        self.bucket_moment_com[:] = self.soil.get_resultant_moment()

        # Zero out forces for invalid soil states
        self.bucket_force_com[self.inter_decimation_soil_model_invalid.squeeze()] = 0.0
        self.bucket_moment_com[self.inter_decimation_soil_model_invalid.squeeze()] = 0.0

        # Clip forces and moments to reasonable values to avoid instability
        self.bucket_force_com[:] = torch.clip(
            self.bucket_force_com, -200000.0, 200000.0
        )
        self.bucket_moment_com[:] = torch.clip(
            self.bucket_moment_com, -200000.0, 200000.0
        )

        if not torch.isfinite(self.bucket_force_com).all():
            print("Warning: Non-finite forces detected in soil model")
        if not torch.isfinite(self.bucket_moment_com).all():
            print("Warning: Non-finite moments detected in soil model")

        # Reshape forces and torques to match expected format for setting external forces
        forces_reshaped = self.bucket_force_com.unsqueeze(1)  # Shape: (num_envs, 1, 3)
        torques_reshaped = self.bucket_moment_com.unsqueeze(
            1
        )  # Shape: (num_envs, 1, 3)

        # Get the bucket body index
        body_ids = torch.tensor(
            [self.m545_measurements.bucket_body_idx[0]], device=self.device
        )

        # Apply forces to the articulation
        self.m545_asset.set_external_force_and_torque(
            forces_reshaped, torques_reshaped, body_ids=body_ids, env_ids=None
        )

    def _reset_idx(self, env_ids: Sequence[int]):
        """Reset environments based on specified indices.

        Args:
            env_ids: List of environment ids which must be reset
        """
        # ---------- Excavation Env specific

        # update the curriculum for environments that need a reset
        # self.curriculum_manager.compute(env_ids=env_ids)# NOTE: Removed here since done in algorithm loop
        self.curriculum_excavation.update_curriculum_excavation()
        # reset the internal buffers of the scene elements
        self.scene.reset(env_ids)
        # randomize the MDP for environments that need a reset, rejection sampling for excavation
        if "reset" in self.event_manager.available_modes:
            env_step_count = self._sim_step_counter // self.cfg.decimation
            self.event_manager.apply(
                env_ids=env_ids, mode="reset", global_env_step_count=env_step_count
            )

        # iterate over all managers and reset them
        # this returns a dictionary of information which is stored in the extras
        # note: This is order-sensitive! Certain things need be reset before others.
        self.extras["log"] = dict()
        # -- observation manager
        info = self.observation_manager.reset(env_ids)
        self.extras["log"].update(info)
        # -- action manager
        info = self.action_manager.reset(env_ids)
        self.extras["log"].update(info)
        # -- rewards manager
        # info = self.reward_manager.reset(env_ids) # To get data: reward over episode lenth
        info = self.reward_manager.reset_excavation(
            env_ids
        )  # To get data: reward for certain termination conition
        self.extras["log"].update(info)
        # -- curriculum manager
        # info = self.curriculum_manager.reset(env_ids) # NOTE: This is replaced by the line below
        info = self.curriculum_excavation.reset_excavation()
        self.extras["log"].update(info)
        # -- command manager
        info = self.command_manager.reset(env_ids)  # ok since empty
        self.extras["log"].update(info)
        # -- randomization manager
        info = self.event_manager.reset(env_ids)  # ok since empty
        self.extras["log"].update(info)
        # -- termination manager
        # info = self.termination_manager.reset(env_ids) # NOTE: This is replaced by the line below

        # Reset debug visualization if needed
        if hasattr(self, "debug_visualization") and len(env_ids) > 0:
            self.debug_visualization.reset()
        # self.extras["log"].update(dict_terms)
        info = self.termination_excavation.reset_excavation(env_ids)
        self.extras["log"].update(info)
        # -- termination manager
        # info = self.termination_manager.reset(env_ids)  # NOTE: This is replaced by the line below
        # self.extras["log"].update(info)
        # reset the episode length buffer
        self.episode_length_buf[env_ids] = 0
        # --.. Extra for Excavation
        self.reset_buf[env_ids] = 1
        # send timeout info to the algorithm
        if self.cfg.send_timeouts:
            self.extras["time_outs"] = self.termination_excavation.time_out_buf

    def reset(
        self, seed: int | None = None, options: dict[str, Any] | None = None
    ) -> tuple[dict, dict]:
        """Resets all the environments and returns observations.

        Args:
            seed: The seed to use for randomization. Defaults to None, in which case the seed is not set.
            options: Additional information to specify how the environment is reset. Defaults to None.

                Note:
                    This argument is used for compatibility with Gymnasium environment definition.
                    Excavtion: Added

        Returns:
            A tuple containing the observations and extras.
        """
        # Update the curriculum when reseted
        self.curriculum_excavation.update_curriculum_excavation()  # Excavation
        # set the seed
        if seed is not None:
            self.seed(seed)
        # reset state of scene
        indices = torch.arange(self.num_envs, dtype=torch.int64, device=self.device)
        self._reset_idx(indices)
        self.m545_measurements.update_measurements()
        self.update_derived_measurements()  # Excavation

        # Initialize soil visualization if first time or force update
        if self.sim.has_gui() and hasattr(self, "initialize_soil_visualization"):
            if (
                not hasattr(self, "soil_visualization_initialized")
                or not self.soil_visualization_initialized
            ):
                self.initialize_soil_visualization()
                self.soil_visualization_initialized = True
            else:
                # Flag for mesh update on next visualization pass
                self.needs_full_mesh_update = True

        # TODO: what is this
        self.extras["episode_pos_term_counts"] = (
            self.termination_excavation.episode_pos_term_counts
        )
        self.extras["episode_neg_term_counts"] = (
            self.termination_excavation.episode_neg_term_counts
        )
        # return observations
        self.obs_buf = self.observation_manager.compute()
        return self.obs_buf, self.extras

    def draw_debug_vis(self):
        """Draw debug visualization for the excavation environment."""

        if not self.sim.has_gui():
            return

        # Check if we need to update the soil mesh
        if self._should_update_soil_mesh():
            self._update_soil_mesh()
            # Return early if we just updated the mesh, as line drawing might use old data otherwise?
            # Or, ensure mesh update happens *after* line drawing if lines need latest data.
            # For now, let's assume line drawing can proceed.

        # Get bucket position for visualization
        bucket_pos_w = self.m545_measurements.bucket_pos_w  # Shape [num_envs, 3]

        # Define x points for line visualization
        num_x_points = 40
        # Rename to avoid confusion in the loop
        x_points_single = torch.linspace(
            self.soil.soil_height.x_min,
            self.soil.soil_height.x_max,
            num_x_points,
            device=self.device,
        )  # Shape [40]

        if self.sim.has_gui():
            self.excavation_draw_interface.clear_lines()

            # Prepare x and y coordinates for get_height in [N, Pts] format
            x_coords_for_height = x_points_single.unsqueeze(0).expand(
                self.num_envs, -1
            )  # Shape [N, Pts]
            # Use each env's bucket y-pos for its line visualization points
            # If lines at y=0 were desired use: torch.zeros_like(x_coords_for_height)
            y_coords_for_height = bucket_pos_w[:, 1:2].expand(
                -1, num_x_points
            )  # Shape [N, Pts]

            # Get heights and max depths for all environments directly in [N, Pts] format
            all_heights = self.soil.soil_height.get_height(
                x_coords_for_height, y_coords_for_height, env_ids=...
            )  # Should return [N, Pts]
            all_max_depths = self.soil.max_depth_height.get_height(
                x_coords_for_height, y_coords_for_height, env_ids=...
            )  # Should return [N, Pts]

            # Draw lines for each environment
            for env_idx in range(self.num_envs):
                # Draw soil surface line (pass the 1D x_points and the corresponding 1D heights)
                self._draw_soil_line(
                    x_points_single,
                    all_heights[env_idx],
                    env_idx,
                    color=[0.0, 1.0, 0.0, 1.0],
                )

                # Draw max depth line
                self._draw_soil_line(
                    x_points_single,
                    all_max_depths[env_idx],
                    env_idx,
                    color=[1.0, 0.0, 0.0, 1.0],
                )

                # Draw bucket position marker for each env
                if hasattr(self, "excavation_visualizer"):
                    self._draw_bucket_markers(env_idx)

    def _draw_soil_line(self, x_points, heights, env_idx, color):
        """Helper method to draw a soil line with specified color"""
        vertices_source = torch.zeros((x_points.shape[0] - 1, 3), device=self.device)
        vertices_target = torch.zeros((x_points.shape[0] - 1, 3), device=self.device)

        vertices_source[:, 0] = x_points[:-1] + self.scene.env_origins[env_idx, 0]
        vertices_source[:, 1] = self.scene.env_origins[env_idx, 1]
        vertices_source[:, 2] = heights[:-1]

        vertices_target[:, 0] = x_points[1:] + self.scene.env_origins[env_idx, 0]
        vertices_target[:, 1] = self.scene.env_origins[env_idx, 1]
        vertices_target[:, 2] = heights[1:]

        num_lines = vertices_source.shape[0]
        lines_colors = [color] * num_lines
        line_thicknesses = [4.0] * num_lines

        self.excavation_draw_interface.draw_lines(
            vertices_source.tolist(),
            vertices_target.tolist(),
            lines_colors,
            line_thicknesses,
        )

    def _draw_bucket_markers(self, env_idx):
        """Helper method to draw bucket position markers"""
        zero_orientation = torch.tensor([1, 0, 0, 0], device=self.device).expand(1, -1)

        # Bucket edge marker
        bucket_edge_pos = self.m545_measurements.bucket_pos_map[env_idx : env_idx + 1]

        # COM marker
        bucket_com_pos = torch.stack(
            (
                self.m545_measurements.bucket_com_pos_w[env_idx, 0]
                + self.scene.env_origins[env_idx, 0],
                self.m545_measurements.bucket_com_pos_w[env_idx, 1]
                + self.scene.env_origins[env_idx, 1],
                self.m545_measurements.bucket_com_pos_w[env_idx, 2]
                + self.scene.env_origins[env_idx, 2],
            )
        ).unsqueeze(0)

        # Visualize the markers
        marker_indices = torch.tensor([0, 6], device=self.device)
        marker_locations = torch.cat([bucket_edge_pos, bucket_com_pos], dim=0)
        marker_orientations = torch.cat([zero_orientation, zero_orientation], dim=0)

        self.excavation_visualizer.visualize(
            marker_locations,
            marker_orientations,
            marker_indices=marker_indices,
        )

    def _should_update_soil_mesh(self):
        """Determine if we should update the soil mesh now.

        Updates mesh based on configuration and training mode.
        """
        # Skip during training mode
        if self.train:
            return False

        # Skip if visualization is disabled
        if not self.cfg.soil_model_cfg.enable_visualization:
            return False

        if not hasattr(self, "needs_full_mesh_update") or not hasattr(
            self, "steps_since_last_update"
        ):
            return False

        # Check if forced update is needed
        if self.needs_full_mesh_update:
            self.needs_full_mesh_update = False
            return True

        # Check if enough steps have passed since last update
        if self.steps_since_last_update >= self.min_update_interval:
            return True

        # Check if bucket has moved significantly
        if hasattr(self, "last_vis_update_bucket_pos"):
            bucket_movement = torch.norm(
                self.m545_measurements.bucket_pos_w - self.last_vis_update_bucket_pos,
                dim=1
            )
            if torch.any(bucket_movement > self.movement_threshold):
                return True

        return False

    def _update_soil_mesh(self):
        """Update the soil mesh using a simplified direct grid approach.

        This method is only called during play_3d mode when visualization is enabled.
        During training mode, visualization is disabled for performance.
        """
        # Only update mesh if we have GUI and visualization is enabled
        if not self.sim.has_gui() or not self.cfg.soil_model_cfg.enable_visualization:
            return

        # Skip mesh updates during training mode for performance
        if self.train:
            return

        print(f"[DEBUG] Updating soil mesh - visualization enabled: {self.cfg.soil_model_cfg.enable_visualization}")

        try:
            # Import necessary modules
            from pxr import UsdGeom, UsdShade, Gf, Sdf
            import omni.usd

            # Get the stage
            stage = omni.usd.get_context().get_stage()

            # Get mesh path
            mesh_path = self.simplified_mesh_path

            # Get grid bounds and dimensions
            grid_bounds = self.soil.get_grid_bounds()
            grid_size_x = self.soil.soil_height.grid_size_x
            grid_size_y = self.soil.soil_height.grid_size_y

            # Set visualization grid size
            vis_grid_x = grid_size_x
            vis_grid_y = grid_size_y

            # Create grid points for visualization
            x_points = torch.linspace(
                grid_bounds["x_min"],
                grid_bounds["x_max"],
                vis_grid_x,
                device=self.device,
            )
            y_points = torch.linspace(
                grid_bounds["y_min"],
                grid_bounds["y_max"],
                vis_grid_y,
                device=self.device,
            )

            # Create 2D grid of points
            xx, yy = torch.meshgrid(x_points, y_points, indexing="ij")
            grid_x = xx.reshape(-1, 1)
            grid_y = yy.reshape(-1, 1)

            # Grid shapes for internal reference
            # grid_x shape: [grid_size_x * grid_size_y, 1]
            # grid_y shape: [grid_size_x * grid_size_y, 1]

            # Get heights at these points (use first environment)
            env_id = 0
            # Pass env_id directly to get_height for correct shape handling
            heights = self.soil.soil_height.get_height(grid_x, grid_y, env_ids=env_id)

            # Generate vertices
            vertices = torch.zeros((grid_x.shape[0], 3), device=self.device)

            vertices[:, 0] = grid_x.squeeze()
            vertices[:, 1] = grid_y.squeeze()
            # Explicitly squeeze heights before assignment
            vertices[:, 2] = heights.squeeze()

            # Create indices for grid faces
            faces = []
            for i in range(vis_grid_x - 1):
                for j in range(vis_grid_y - 1):
                    v0 = i * vis_grid_y + j
                    v1 = i * vis_grid_y + (j + 1)
                    v2 = (i + 1) * vis_grid_y + (j + 1)
                    v3 = (i + 1) * vis_grid_y + j

                    # Add two triangles for each grid cell
                    faces.extend([v0, v1, v2])
                    faces.extend([v0, v2, v3])

            # Convert to USD-compatible types
            vertices_gf = [
                Gf.Vec3f(float(v[0]), float(v[1]), float(v[2]))
                for v in vertices.cpu().numpy()
            ]
            face_vertex_counts = [3] * (len(faces) // 3)

            # Check if mesh already exists
            if self.mesh_prim is None:
                # Create mesh prim if it doesn't exist yet
                existing_prim = stage.GetPrimAtPath(mesh_path)
                if existing_prim.IsValid():
                    print(f"[INFO] Using existing mesh at {mesh_path}")
                    self.mesh_prim = UsdGeom.Mesh(existing_prim)
                else:
                    # Create new mesh
                    self.mesh_prim = UsdGeom.Mesh.Define(stage, mesh_path)

                    # Create soil material
                    soil_color = Gf.Vec3f(0.6, 0.4, 0.2)  # Brown soil color
                    material = UsdShade.Material.Define(stage, f"{mesh_path}/Material")
                    shader = UsdShade.Shader.Define(
                        stage, f"{mesh_path}/Material/Shader"
                    )
                    shader.CreateIdAttr("UsdPreviewSurface")
                    shader.CreateInput("diffuseColor", Sdf.ValueTypeNames.Color3f).Set(
                        soil_color
                    )
                    shader.CreateInput("roughness", Sdf.ValueTypeNames.Float).Set(0.8)
                    shader.CreateInput("metallic", Sdf.ValueTypeNames.Float).Set(0.0)
                    material.CreateSurfaceOutput().ConnectToSource(
                        shader.ConnectableAPI(), "surface"
                    )

                    # Bind material to mesh
                    UsdShade.MaterialBindingAPI(self.mesh_prim).Bind(material)

            # Update mesh attributes
            self.mesh_prim.CreatePointsAttr(vertices_gf)
            self.mesh_prim.CreateFaceVertexCountsAttr(face_vertex_counts)
            self.mesh_prim.CreateFaceVertexIndicesAttr(faces)

            # Clean up memory - explicitly delete large tensors
            del vertices, grid_x, grid_y, xx, yy, x_points, y_points, heights
            torch.cuda.empty_cache()  # Force CUDA memory cleanup

            # Reset update tracking
            self.steps_since_last_update = 0
            self.last_vis_update_bucket_pos.copy_(self.m545_measurements.bucket_pos_w)

            # Uncomment for debugging if needed
            # print(f"[INFO] Updated soil mesh with {len(vertices_gf)} vertices and {len(faces) // 3} triangles")
        except Exception as e:
            print(f"[ERROR] Failed to update soil mesh: {e}")
            # Continue execution even if mesh update fails

    def initialize_soil_visualization(self):
        """Initialize a simplified soil visualization that directly renders the height field grid.

        This method is only called during play_3d mode when visualization is enabled.
        During training mode, this method returns early to avoid unnecessary initialization.
        """
        # Only initialize visualization if we have GUI and visualization is enabled
        if not self.sim.has_gui() or not self.cfg.soil_model_cfg.enable_visualization:
            return

        # Skip visualization initialization during training mode
        if self.train:
            print("[INFO] Skipping soil visualization initialization during training mode")
            return

        print(
            "[INFO] Initializing 3D soil mesh visualization for play_3d mode"
        )
        print(f"[INFO] Visualization config - enabled: {self.cfg.soil_model_cfg.enable_visualization}")
        print(f"[INFO] Training mode: {self.train}")

        # Import necessary modules for visualization
        try:
            # USD imports are handled inside _update_soil_mesh method

            # Initialize tracking variables
            self.needs_full_mesh_update = True
            self.steps_since_last_update = 0
            self.min_update_interval = self.cfg.soil_model_cfg.min_update_interval
            self.movement_threshold = 0.5  # Set a default if not specified
            self.last_vis_update_bucket_pos = (
                self.m545_measurements.bucket_pos_w.clone()
            )

            # Create a mesh path under the specific environment prim (env_0)
            self.simplified_mesh_path = "/World/envs/env_0/SoilSimpleMesh"

            # Storage for mesh primitive
            self.mesh_prim = None

            # Generate initial mesh
            self._update_soil_mesh()
        except Exception as e:
            print(f"[ERROR] Failed to initialize soil visualization: {e}")
            # Set visualization as disabled to prevent further attempts
            self.cfg.soil_model_cfg.enable_visualization = False
