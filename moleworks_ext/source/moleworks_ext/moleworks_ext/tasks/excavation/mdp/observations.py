# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause
import torch
from typing import TYPE_CHECKING

from isaaclab.assets import Articulation, RigidObject
from isaaclab.envs import ManagerBasedEnv
from isaaclab.managers import SceneEntityCfg
import isaacsim.core.utils.torch as torch_utils


def dof_pos(env: ManagerBasedEnv, asset_cfg: SceneEntityCfg = SceneEntityCfg("robot")) -> torch.Tensor:
    result = env.m545_measurements.arm_joint_pos
    # print("dof_pos:", result)
    return result


def dof_vel(env: ManagerBasedEnv, asset_cfg: SceneEntityCfg = SceneEntityCfg("robot")) -> torch.Tensor:
    result = env.m545_measurements.arm_joint_vel
    # print("dof_vel:", result)
    return result


def dof_tau(env) -> torch.Tensor:
    result = env.m545_measurements.arm_joint_tau
    # print("dof_tau:", result)
    return result


def last_action_excavation(env) -> torch.Tensor:
    result = env.actions
    # print("last_action_excavation:", result)
    return result


def bucket_lin_gac(env) -> torch.Tensor:
    result = torch.cat(
        (env.m545_measurements.bucket_pos_base[:, [0, 2]], env.m545_measurements.bucket_vel_base[:, [0, 2]]), dim=-1
    )
    # print("bucket_lin_gac:", result)
    return result


def bucket_lin_gac_base(env) -> torch.Tensor:
    return torch.cat(
        (env.m545_measurements.bucket_pos_base[:, [0, 2]], env.m545_measurements.bucket_vel_base[:, [0, 2]]), dim=-1
    )

def bucket_ang_gac(env) -> torch.Tensor:
    result = torch.cat(
        (env.m545_measurements.bucket_ang_gac.unsqueeze(-1), env.m545_measurements.bucket_ang_vel_base[:, 1:2]), dim=-1
    )
    # print("bucket_ang_gac:", result)
    return result


def bucket_lin_vel_norm(env) -> torch.Tensor:
    result = env.m545_measurements.bucket_vel_norm.unsqueeze(-1)
    # print("bucket_lin_vel_norm:", result)
    return result


def base_pitch_gac(env) -> torch.Tensor:
    result = env.m545_measurements.base_pitch_w.unsqueeze(-1)
    # print("base_pitch_gac:", result)
    return result


def fill_ratio_aoa(env) -> torch.Tensor:
    result = torch.cat((env.soil.get_fill_ratio(), env.m545_measurements.bucket_aoa.unsqueeze(-1)), dim=-1)
    # print("fill_ratio_aoa:", result)
    # print("aoa:", env.m545_measurements.bucket_aoa)
    return result


def fill_ratio(env) -> torch.Tensor:
    result = env.soil.get_fill_ratio()
    # print("fill_ratio:", result)
    return result


def aoa(env) -> torch.Tensor:
    result = env.m545_measurements.bucket_aoa.unsqueeze(-1)
    # print("aoa:", result)
    return result


def soil_height(env) -> torch.Tensor:
    # Transform soil height from world to base frame
    world_heights = env.soil_height_futures
    # print("world_heights shape:", world_heights.shape)  # Print the shape of world_heights
    # Ensure the dimensions match by expanding root_pos_w if necessary
    root_pos_w_expanded = env.m545_measurements.root_pos_w[:, 2].unsqueeze(1).expand(-1, world_heights.size(1))
    result = world_heights - root_pos_w_expanded
    # print("soil_height:", result)
    return result


def max_depth(env) -> torch.Tensor:
    # Transform max depth from world to base frame
    world_depths = env.max_depth_futures
    # print("world_depths shape:", world_depths.shape)  # Print the shape of world_depths
    # Ensure the dimensions match by expanding root_pos_w if necessary
    root_pos_w_expanded = env.m545_measurements.root_pos_w[:, 2].unsqueeze(1).expand(-1, world_depths.size(1))
    result = world_depths - root_pos_w_expanded
    # print("max_depth:", result)
    return result


# def bucket_depth(env) -> torch.Tensor:
#     result = env.soil.get_bucket_depth()
#     # print("bucket_depth:", result)
#     return result


def pitch_vel(env) -> torch.Tensor:
    result = env.m545_measurements.j_pitch_vel[:, [0, 2]]
    # print("pitch_vel:", result)
    return result


def pullup_dist(env) -> torch.Tensor:
    result = env.pullup_dist.unsqueeze(-1)
    # print("pullup_dist:", result)
    return result


def soil_parameters(env) -> torch.Tensor:
    res_non_scaled = env.soil.get_soil_params()[:, 0 : env.soil.get_n_soil_params_to_sample()]
    # print("soil_parameters:", res_non_scaled)
    return res_non_scaled


def bucket_spatial_perception(env) -> torch.Tensor:
    """
    Practical spatial perception for excavation: 5 points across bucket width × 5 points ahead.

    This provides spatial awareness around the bucket for excavation planning:
    - 5 points across the bucket width (based on actual bucket width ~1.4m)
    - 5 points ahead of the bucket (1.2m forward coverage)
    - 30cm spacing between points
    - Total: 25 dimensions (5×5 grid)

    Returns:
        torch.Tensor: Soil heights in bucket-relative grid [n_envs, 25] relative to base frame
    """
    # Grid configuration optimized for excavation
    points_width = 5  # Points across bucket width (spans ~1.4m bucket width)
    points_forward = 5  # Points ahead of bucket
    spacing = 0.3  # 30cm spacing between points

    # Get bucket position and orientation in world frame
    bucket_pos_w = env.m545_measurements.bucket_pos_w  # [n_envs, 3]

    # Calculate bucket-relative sampling points
    # Width: centered on bucket, spanning bucket width
    width_extent = (points_width - 1) * spacing / 2  # ±0.6m from bucket center
    width_offsets = torch.linspace(-width_extent, width_extent, points_width, device=env.device)

    # Forward: starting from bucket tip, going forward
    forward_offsets = torch.arange(0, points_forward, device=env.device) * spacing  # 0, 0.3, 0.6, 0.9, 1.2m

    # Create sampling grid in bucket-relative coordinates
    # X-direction: forward from bucket (positive x)
    # Y-direction: across bucket width (left-right)
    x_grid, y_grid = torch.meshgrid(forward_offsets, width_offsets, indexing='ij')

    # Flatten for batch processing
    x_flat = x_grid.flatten()  # [25] - forward distances
    y_flat = y_grid.flatten()  # [25] - lateral distances

    # Transform to world coordinates relative to bucket position
    x_query = bucket_pos_w[:, 0:1] + x_flat.unsqueeze(0)  # [n_envs, 25]
    y_query = bucket_pos_w[:, 1:2] + y_flat.unsqueeze(0)  # [n_envs, 25]

    # Get soil heights at sampling points
    if hasattr(env.soil, 'get_soil_height_at_pos'):
        # 3D soil model - use both x and y coordinates
        soil_heights_w = env.soil.get_soil_height_at_pos(x_query, y_query)
    else:
        # 1D soil model - use only x coordinates (forward direction)
        soil_heights_w = env.soil.get_soil_height_at_pos(x_query)

    # Transform to base frame (relative to robot base)
    root_pos_w_expanded = env.m545_measurements.root_pos_w[:, 2].unsqueeze(1).expand(-1, points_width * points_forward)
    soil_heights_base = soil_heights_w - root_pos_w_expanded

    return soil_heights_base


def bucket_forward_perception(env) -> torch.Tensor:
    """
    Simplified forward-looking perception: 5 points ahead of bucket.

    This is a minimal upgrade from the current 1D perception:
    - 5 points directly ahead of bucket
    - 30cm spacing (0, 0.3, 0.6, 0.9, 1.2m forward)
    - Compatible with both 2D and 3D soil models
    - Total: 5 dimensions

    Returns:
        torch.Tensor: Soil heights ahead of bucket [n_envs, 5] relative to base frame
    """
    # Configuration
    num_points = 5
    spacing = 0.3  # 30cm spacing

    # Get bucket position in world frame
    bucket_pos_w = env.m545_measurements.bucket_pos_w  # [n_envs, 3]

    # Create forward sampling points (0, 0.3, 0.6, 0.9, 1.2m ahead)
    forward_distances = torch.arange(0, num_points, device=env.device) * spacing

    # Sample points ahead of bucket
    x_query = bucket_pos_w[:, 0:1] + forward_distances.unsqueeze(0)  # [n_envs, 5]
    y_query = bucket_pos_w[:, 1:2].expand(-1, num_points)  # [n_envs, 5] - same y as bucket

    # Get soil heights at sampling points
    if hasattr(env.soil, 'get_soil_height_at_pos'):
        # 3D soil model
        soil_heights_w = env.soil.get_soil_height_at_pos(x_query, y_query)
    else:
        # 1D soil model - use only x coordinates
        soil_heights_w = env.soil.get_soil_height_at_pos(x_query)

    # Transform to base frame
    root_pos_w_expanded = env.m545_measurements.root_pos_w[:, 2].unsqueeze(1).expand(-1, num_points)
    soil_heights_base = soil_heights_w - root_pos_w_expanded

    return soil_heights_base


def bucket_angle_of_attack_obs(env) -> torch.Tensor:
    """
    Bucket angle of attack as both observation and potential termination condition.

    Returns:
        torch.Tensor: Bucket angle of attack [n_envs, 1]
    """
    return env.m545_measurements.bucket_aoa.unsqueeze(-1)


def max_depth_spatial_perception(env) -> torch.Tensor:
    """
    Spatial max depth perception using the same 5x5 grid approach as spatial_perception.

    This provides consistent spatial max depth information around the bucket:
    - 5 points across the bucket width (based on actual bucket width ~1.4m)
    - 5 points ahead of the bucket (1.2m forward coverage)
    - 30cm spacing between points
    - Total: 25 dimensions (5×5 grid)

    Returns:
        torch.Tensor: Max depths in bucket-relative grid [n_envs, 25] relative to base frame
    """
    # Grid configuration - same as bucket_spatial_perception for consistency
    points_width = 5  # Points across bucket width (spans ~1.4m bucket width)
    points_forward = 5  # Points ahead of bucket
    spacing = 0.3  # 30cm spacing between points

    # Get bucket position and orientation in world frame
    bucket_pos_w = env.m545_measurements.bucket_pos_w  # [n_envs, 3]

    # Calculate bucket-relative sampling points
    # Width: centered on bucket, spanning bucket width
    width_extent = (points_width - 1) * spacing / 2  # ±0.6m from bucket center
    width_offsets = torch.linspace(-width_extent, width_extent, points_width, device=env.device)

    # Forward: starting from bucket tip, going forward
    forward_offsets = torch.arange(0, points_forward, device=env.device) * spacing  # 0, 0.3, 0.6, 0.9, 1.2m

    # Create sampling grid in bucket-relative coordinates
    # X-direction: forward from bucket (positive x)
    # Y-direction: across bucket width (left-right)
    x_grid, y_grid = torch.meshgrid(forward_offsets, width_offsets, indexing='ij')

    # Flatten for batch processing
    x_flat = x_grid.flatten()  # [25] - forward distances
    y_flat = y_grid.flatten()  # [25] - lateral distances

    # Transform to world coordinates relative to bucket position
    x_query = bucket_pos_w[:, 0:1] + x_flat.unsqueeze(0)  # [n_envs, 25]
    y_query = bucket_pos_w[:, 1:2] + y_flat.unsqueeze(0)  # [n_envs, 25]

    # Get max depths at sampling points
    if hasattr(env.soil, 'get_max_depth_at_pos'):
        # 3D soil model - use both x and y coordinates
        max_depths_w = env.soil.get_max_depth_at_pos(x_query, y_query)
    elif hasattr(env, 'max_depth_futures'):
        # Use existing max_depth_futures but interpolate to grid points
        # For now, replicate the pattern from the original max_depth function
        # This creates a spatial representation by replicating max depth values
        original_max_depths = env.max_depth_futures  # [n_envs, 5]

        # Create spatial grid by interpolating/replicating the 5 original points to 25 grid points
        # Map the 5 forward points to the 5x5 grid (each row gets the same forward depth)
        spatial_max_depths = torch.zeros(env.num_envs, points_width * points_forward, device=env.device)

        for i in range(points_forward):
            # For each forward position, replicate across the width
            start_idx = i * points_width
            end_idx = start_idx + points_width
            if i < original_max_depths.shape[1]:
                spatial_max_depths[:, start_idx:end_idx] = original_max_depths[:, i:i+1].expand(-1, points_width)
            else:
                # If we have fewer original points, use the last available point
                spatial_max_depths[:, start_idx:end_idx] = original_max_depths[:, -1:].expand(-1, points_width)

        max_depths_w = spatial_max_depths
    else:
        # Fallback: create zero depths
        max_depths_w = torch.zeros(env.num_envs, points_width * points_forward, device=env.device)

    # Transform to base frame (relative to robot base)
    root_pos_w_expanded = env.m545_measurements.root_pos_w[:, 2].unsqueeze(1).expand(-1, points_width * points_forward)
    max_depths_base = max_depths_w - root_pos_w_expanded

    return max_depths_base

