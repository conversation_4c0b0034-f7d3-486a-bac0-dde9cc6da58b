from __future__ import annotations

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .soil_3d import Soil3D

import numpy as np
import torch


class BucketState3D:
    """Represents the state of the bucket in 3D, including its position,
    orientation, fill ratio, and interaction with the soil.
    """

    def __init__(self, soil_model: Soil3D, cfg):
        """Initialize the 3D bucket state.

        Args:
            soil_model: The parent soil model
            cfg: Configuration object
        """
        self.SM = soil_model
        self.n_envs = self.SM.n_envs
        self.device = self.SM.device
        self.cfg = cfg.bucket

        """
        Constants
        """
        # Bucket geometry parameters
        self.r = torch.tensor(self.cfg.r, device=self.device)
        self.b = torch.tensor(self.cfg.b, device=self.device)  # bucket width
        self.a = torch.tensor(self.cfg.a, device=self.device)  # bottom plate length

        # Discretize bottom plate for depth calculations
        self.a_discretized = torch.arange(
            0, self.cfg.a + 1e-5, self.cfg.da, device=self.device
        )
        self.a_discretized_expanded = self.a_discretized.expand(self.n_envs, -1)

        # Y-direction discretization for accurate 3D soil collection
        self.num_y_segments = 50  # Discretize bucket width into 50 segments
        self.dy_segment = self.b / self.num_y_segments
        self.y_offsets = torch.linspace(
            -self.b / 2.0, self.b / 2.0, self.num_y_segments, device=self.device
        )  # Y offsets from bucket center

        # Maximum fill area (2D projection)
        self.max_fill_area = np.pi / 2.0 * torch.square(self.r) + self.a * self.r
        self.max_fill_area_expanded = self.max_fill_area * torch.ones(
            self.n_envs, 1, device=self.device
        )

        # Maximum fill volume (actual 3D volume)
        self.max_fill_volume = self.max_fill_area * self.b

        # 3D soil accumulation: track soil volume per Y-segment
        self.soil_volume_per_segment = torch.zeros(
            self.n_envs, self.num_y_segments, device=self.device
        )
        self.max_volume_per_segment = self.max_fill_area * self.dy_segment

        # Utility tensors
        self.zero_vec = torch.zeros(self.n_envs, 1, device=self.device)

        # Angle between bottom plate and completely full top (max ssp angle)
        self.alpha_max = torch.atan2(2.0 * self.r, self.a)

        # Bucket edge parameters
        self.top_width = torch.tensor(self.cfg.edge_top_width, device=self.device)
        self.half_angle = torch.tensor(self.cfg.edge_half_angle, device=self.device)

        """
        Bucket internal variable buffers
        """
        # Is bottom plate in soil?
        self.bp_in_soil = torch.zeros(
            self.n_envs, 1, device=self.device, dtype=torch.bool
        )
        # Bottom plate length in soil
        self.bp_soil = torch.zeros(self.n_envs, 1, device=self.device)

        # Angles
        self.bp_angle_to_horizon = torch.zeros(self.n_envs, 1, device=self.device)
        self.full_angle_to_horizon = torch.zeros(self.n_envs, 1, device=self.device)

        # Fill state (maintained for backward compatibility)
        self.fill_area = torch.zeros(self.n_envs, 1, device=self.device)
        self.swept_area = torch.zeros(self.n_envs, 1, device=self.device)
        self.fill_ratio = torch.zeros(self.n_envs, 1, device=self.device)

        # Total soil volume (sum across all Y-segments)
        self.total_soil_volume = torch.zeros(self.n_envs, 1, device=self.device)

        # Depth variables
        self.depth = torch.zeros(self.n_envs, 1, device=self.device)
        self.clipped_depth = torch.zeros(self.n_envs, 1, device=self.device)
        self.prev_depth = torch.zeros(self.n_envs, 1, device=self.device)
        self.clipped_prev_depth = torch.zeros(self.n_envs, 1, device=self.device)
        self.clipped_average_depth_bp = torch.zeros(self.n_envs, 1, device=self.device)

        # Velocity dot product and cosine
        self.vel_dot = torch.zeros(self.n_envs, 1, device=self.device)
        self.vel_cos = torch.zeros(self.n_envs, 1, device=self.device)

        # 3D specific variables
        self.COM_pos_w = torch.zeros(self.n_envs, 3, device=self.device)

    def update(self, idxs=...):
        """Update the bucket state based on the current position and soil interaction.

        Args:
            idxs: Indices of environments to update
        """
        with torch.profiler.record_function("BucketState3D.update"):
            self._update_geometry()
            self._update_bp_in_soil()
            self._update_filling(idxs)

    def set_fill_state(self, filling, env_ids=..., is_ratio=True):
        """Set fill ratio or fill area directly and swept area.

        Args:
            filling: Either area or ratio [0-1]
            env_ids: Indices of environments to update
            is_ratio: If True, filling is a ratio [0-1], otherwise it's an area
        """
        if env_ids == ...:
            dim0 = self.n_envs
        else:
            dim0 = len(env_ids)

        if dim0 == 0:
            return

        filling = torch.clip(
            filling,
            torch.zeros(1, device=self.device),
            torch.ones(1, device=self.device),
        )

        if is_ratio:
            self.fill_ratio[env_ids] = filling
            self.fill_area[env_ids] = filling * self.max_fill_area
            self.swept_area[env_ids] = filling * self.max_fill_area
            # Update 3D soil volume distribution (uniform across Y-segments)
            target_volume = filling * self.max_fill_volume
            self.soil_volume_per_segment[env_ids] = (
                target_volume.view(-1, 1) / self.num_y_segments
            )
            self.total_soil_volume[env_ids] = target_volume
        else:
            self.fill_area[env_ids] = torch.clip(
                filling, torch.zeros(1, device=self.device), self.max_fill_area
            )
            self.swept_area[env_ids] = self.fill_area.clone()[env_ids]
            self.fill_ratio[env_ids] = self.fill_area[env_ids] / self.max_fill_area
            # Update 3D soil volume distribution
            target_volume = self.fill_area[env_ids] * self.b
            self.soil_volume_per_segment[env_ids] = (
                target_volume.view(-1, 1) / self.num_y_segments
            )
            self.total_soil_volume[env_ids] = target_volume

    def _update_geometry(self):
        """Update geometric properties of the bucket based on its current position and orientation."""
        with torch.profiler.record_function("BucketState3D._update_geometry"):
            # Calculate bottom plate angle to horizon
            # Use 3D unit vector but extract the XZ plane components for angle calculation
            self.bp_angle_to_horizon[:] = torch.atan2(
                self.SM.bp_unit_vector_w[:, 2],
                self.SM.bp_unit_vector_w[:, 0],
            ).view(-1, 1)

            # Calculate back position - use the 3D representations
            # Project the unit vector onto the XZ plane for compatibility
            bp_unit_xz = torch.zeros_like(self.SM.bp_unit_vector_w)
            bp_unit_xz[:, 0] = self.SM.bp_unit_vector_w[:, 0]
            bp_unit_xz[:, 2] = self.SM.bp_unit_vector_w[:, 2]
            bp_unit_xz = bp_unit_xz / (bp_unit_xz.norm(dim=1, keepdim=True) + 1e-5)

            # Calculate back position in 3D
            back_pos_w = self.SM.bucket_pos_w + bp_unit_xz * (self.a + self.r).view(-1, 1)

            # Store legacy 2D back position for backward compatibility
            self.back_pos_w = torch.stack([back_pos_w[:, 0], back_pos_w[:, 2]], dim=1)

            # Calculate full angle to horizon
            self.full_angle_to_horizon[:] = (
                self.bp_angle_to_horizon + self.alpha_max
            ).view(-1, 1)

            # Calculate depth at bucket tip (negative depth = outside soil)
            # Use 3D get_height_at_point method
            bucket_x = self.SM.bucket_pos_w[:, 0].view(-1, 1)
            bucket_y = self.SM.bucket_pos_w[:, 1].view(-1, 1)

            # Get height at current bucket position
            current_heights = self.SM.soil_height.get_height(bucket_x, bucket_y)

            # Extract the heights directly - no need for a loop
            # Squeeze to remove the extra dimension since we only have one point per environment
            heights_for_envs = current_heights.squeeze(1)

            # Calculate depth using Z coordinate from 3D position
            self.depth[:] = heights_for_envs.view(-1, 1) - self.SM.bucket_pos_w[:, 2].view(
                -1, 1
            )
            self.clipped_depth[:] = torch.where(self.depth < 0.0, self.zero_vec, self.depth)

            # Handle previous position - use 3D positions
            prev_bucket_x = self.SM.prev_bucket_pos_w[:, 0].view(-1, 1)
            prev_bucket_y = self.SM.prev_bucket_pos_w[:, 1].view(-1, 1)

            # Get height at previous bucket position
            prev_heights = self.SM.soil_height.get_height(prev_bucket_x, prev_bucket_y)

            # Extract the heights directly - no need for a loop
            # Squeeze to remove the extra dimension since we only have one point per environment
            prev_heights_for_envs = prev_heights.squeeze(1)

            # Calculate previous depth using Z coordinate from 3D position
            self.prev_depth[:] = prev_heights_for_envs.view(
                -1, 1
            ) - self.SM.prev_bucket_pos_w[:, 2].view(-1, 1)
            self.clipped_prev_depth[:] = torch.where(
                self.prev_depth < 0.0, self.zero_vec, self.prev_depth
            )

            # Calculate velocity projection on bottom plate - use 3D vectors
            # Project the 3D velocity onto the bottom plate unit vector
            vel_dot_3d = torch.sum(
                self.SM.bucket_vel_w * (-self.SM.bp_unit_vector_w), dim=1, keepdim=True
            )
            self.vel_dot[:] = vel_dot_3d

            # Calculate velocity cosine with improved numerical stability
            bp_norm = self.SM.bp_unit_vector_w.norm(dim=1, keepdim=True)
            vel_norm = self.SM.bucket_vel_w.norm(dim=1, keepdim=True)

            # Use a larger epsilon for better numerical stability
            epsilon = 1e-4

            # Only calculate cosine when velocity is significant
            # This prevents erratic behavior when velocity is near zero
            vel_significant = vel_norm > epsilon

            # Where velocity is significant, calculate cosine normally
            # Where velocity is insignificant, set cosine to zero
            self.vel_cos[:] = torch.where(
                vel_significant,
                self.vel_dot / (bp_norm * vel_norm + epsilon),
                torch.zeros_like(self.vel_dot)
            )

            # Clamp values to valid cosine range [-1, 1] to handle any numerical errors
            self.vel_cos[:] = torch.clamp(self.vel_cos, -1.0, 1.0)

            # Update COM position in world frame - use full 3D position
            self.COM_pos_w = self.SM.bucket_com_pos_w

    def _update_filling(self, idxs=...):
        """Update bucket filling based on movement through soil with accurate 3D collection.

        Args:
            idxs: Indices of environments to update
        """
        with torch.profiler.record_function("BucketState3D._update_filling"):
            # Calculate movement in all 3 directions
            dx = (self.SM.bucket_pos_w[idxs, 0] - self.SM.prev_bucket_pos_w[idxs, 0])
            dy = (self.SM.bucket_pos_w[idxs, 1] - self.SM.prev_bucket_pos_w[idxs, 1])
            dz = (self.SM.bucket_pos_w[idxs, 2] - self.SM.prev_bucket_pos_w[idxs, 2])

            # Get number of environments to update
            if idxs == ...:
                n_envs_update = self.n_envs
                env_indices = torch.arange(self.n_envs, device=self.device)
            else:
                n_envs_update = len(idxs)
                env_indices = idxs

            if n_envs_update == 0:
                return

            # 3D soil collection across Y-segments
            self._collect_soil_3d(env_indices, dx, dy, dz)

            # Handle soil spilling
            self._handle_soil_spilling(env_indices)

            # Update legacy fill state for backward compatibility
            self._update_legacy_fill_state(env_indices)

    def _collect_soil_3d(self, env_indices, dx, dy, dz):
        """Collect soil across Y-segments based on 3D movement and soil heights.

        Args:
            env_indices: Environment indices to update
            dx, dy, dz: Movement components in world frame
        """
        n_envs_update = len(env_indices)

        if n_envs_update == 0:
            return

        # Create sampling points across bucket width for each environment
        # Get bucket positions for the environments being updated
        bucket_pos = self.SM.bucket_pos_w[env_indices]  # [n_envs_update, 3]

        # Create Y offsets for each environment: [n_envs_update, num_y_segments]
        bucket_x = bucket_pos[:, 0].unsqueeze(1).expand(-1, self.num_y_segments)  # [n_envs_update, num_y_segments]
        bucket_y = bucket_pos[:, 1].unsqueeze(1) + self.y_offsets.unsqueeze(0)  # [n_envs_update, num_y_segments]

        # Get soil heights at all sampling points
        # The get_height method expects [n_envs, n_points] format and specific env_ids
        soil_heights = self.SM.soil_height.get_height(bucket_x, bucket_y, env_indices)

        # Calculate depth at each Y-segment
        bucket_z = self.SM.bucket_pos_w[env_indices, 2].unsqueeze(1)  # [n_envs, 1]
        depths = soil_heights - bucket_z  # [n_envs_update, num_y_segments]
        depths_clipped = torch.clamp(depths, min=0.0)  # Only positive depths

        # Calculate effective movement for soil collection
        effective_dx = torch.clamp(-dx, min=0.0)  # Forward movement collects soil
        effective_dy = torch.abs(dy) * 0.3  # Sideways movement (scaled)
        effective_dz = torch.clamp(dz, min=0.0) * 0.5  # Downward movement
        effective_movement = effective_dx + effective_dy + effective_dz

        # Calculate soil volume collected per segment
        # Volume = effective_movement * depth * segment_width
        volume_collected = (
            effective_movement.unsqueeze(1) * depths_clipped * self.dy_segment
        )  # [n_envs_update, num_y_segments]

        # Update soil volume per segment (accumulate)
        self.soil_volume_per_segment[env_indices] += volume_collected

        # Clip to maximum volume per segment
        self.soil_volume_per_segment[env_indices] = torch.clamp(
            self.soil_volume_per_segment[env_indices],
            max=self.max_volume_per_segment
        )

    def _handle_soil_spilling(self, env_indices):
        """Handle soil spilling when bucket is tilted or above soil surface.

        Args:
            env_indices: Environment indices to update
        """
        if len(env_indices) == 0:
            return

        # Check spilling conditions
        tilted_upward = self.bp_angle_to_horizon[env_indices] > 0.0
        above_soil = self.depth[env_indices] < -self.SM.env.curriculum_excavation.curr_spilling_depth_margin
        moving_upward = self.SM.bucket_vel_w[env_indices, 2] < -0.1

        # Combine conditions for soil loss
        losing_soil = torch.logical_and(
            torch.logical_and(tilted_upward.squeeze(), above_soil.squeeze()),
            moving_upward
        )

        if torch.any(losing_soil):
            # Get the actual environment indices that are losing soil
            losing_env_mask = losing_soil
            losing_env_indices = env_indices[losing_env_mask]

            if len(losing_env_indices) > 0:
                # Apply gradual soil loss (20% per timestep)
                spill_rate = 0.2
                self.soil_volume_per_segment[losing_env_indices] *= (1.0 - spill_rate)

                # Complete spill for steep angles (>30 degrees)
                steep_angle = self.bp_angle_to_horizon[losing_env_indices] > 0.5
                severe_spill_mask = steep_angle.squeeze()

                if torch.any(severe_spill_mask):
                    severe_spill_indices = losing_env_indices[severe_spill_mask]
                    self.soil_volume_per_segment[severe_spill_indices] = 0.0

    def _update_legacy_fill_state(self, env_indices):
        """Update legacy fill state variables for backward compatibility.

        Args:
            env_indices: Environment indices to update
        """
        # Calculate total soil volume
        self.total_soil_volume[env_indices] = self.soil_volume_per_segment[env_indices].sum(dim=1, keepdim=True)

        # Update legacy variables
        self.fill_area[env_indices] = self.total_soil_volume[env_indices] / self.b
        self.swept_area[env_indices] = self.fill_area[env_indices].clone()
        self.fill_ratio[env_indices] = self.fill_area[env_indices] / self.max_fill_area

    def _update_bp_in_soil(self):
        """Compute the length of the bottom plate inside the soil using discretized points."""
        with torch.profiler.record_function("BucketState3D._update_bp_in_soil"):
            # Use 3D positions and unit vectors
            # Generate positions along the bottom plate in 3D
            da_pos_w = self.SM.bucket_pos_w.view(
                self.n_envs, 1, 3
            ) + self.a_discretized.view(-1, 1) * self.SM.bp_unit_vector_w.view(
                self.n_envs, 1, 3
            )

            # Extract components needed for soil height checking
            da_x = da_pos_w[..., 0]  # n_envs, num_da
            da_y = da_pos_w[..., 1]  # n_envs, num_da
            da_z = da_pos_w[..., 2]  # Heights in 3D space

            # Get soil heights at discretized positions - vectorized approach
            # Our updated get_height_at_point function now handles 2D tensors directly
            # Shape of da_x and da_y is [n_envs, num_da] which matches what we need
            da_soil_heights_w = self.SM.soil_height.get_height(da_x, da_y)

            # Find which da_height is larger than soil height
            # If idx is 0: already tip is not in soil
            max_val, max_idxs = torch.max(da_z >= da_soil_heights_w, dim=1, keepdim=True)

            # Handle corner cases
            max_idxs = torch.where(max_val == 0, self.a_discretized.shape[0] - 1, max_idxs)

            # Update state variables
            self.bp_in_soil[:] = max_idxs != 0
            self.bp_soil[:] = torch.gather(self.a_discretized_expanded, 1, max_idxs)

            # Calculate average depth along bottom plate for forces - vectorized approach
            depths = torch.zeros_like(da_x)

            # Create a mask for points that are below the soil
            # Use the same device as other tensors in this method
            below_soil_mask = (
                torch.arange(self.a_discretized.shape[0], device=da_x.device).expand(
                    self.n_envs, -1
                )
                < max_idxs
            )

            # Calculate depth where mask is True
            # Clip negative depths to zero
            raw_depths = da_soil_heights_w - da_z
            positive_depths = torch.clamp(raw_depths, min=0.0)

            # Apply mask to keep only depths for points below soil
            depths = torch.where(
                below_soil_mask, positive_depths, torch.zeros_like(positive_depths)
            )

            # Calculate average depth (excluding zero depths)
            valid_depths = (depths > 0).sum(dim=1, keepdim=True).float()
            valid_depths = torch.where(
                valid_depths < 1.0, torch.ones_like(valid_depths), valid_depths
            )
            self.clipped_average_depth_bp[:] = (
                torch.sum(depths, dim=1, keepdim=True) / valid_depths
            )
