# Moleworks Project Documentation: 3D Soil Model Implementation

## 1. Project Overview

The Moleworks project is a simulation environment for excavator operation, built on top of the NVIDIA Isaac Sim platform. The project focuses on realistic soil simulation for excavation tasks, with particular emphasis on soil-bucket interactions, force calculations, and terrain deformation. The codebase implements both 1D (simplified) and 3D soil models, with the 3D model providing more realistic and detailed simulation capabilities.

### 1.1 Key Components

The codebase is organized into several key components:

1. **Environment Setup**: Defined in `excavation_env.py`, which configures the simulation environment, including physics settings, rendering options, and task parameters.

2. **Soil Models**: Implemented in two versions:
   - **1D Soil Model**: A simplified height-field based soil representation (`soil_model/`)
   - **3D Soil Model**: A more complex volumetric soil representation with BVH-based collision detection (`soil_model_3d/`)

3. **Training and Evaluation**: Implemented in the scripts directory, particularly in `train.py` and `play.py`, which utilize the RSL-RL framework for reinforcement learning.

4. **Utilities**: Various utility functions for visualization, data processing, and mathematical operations.

### 1.2 Technology Stack

- **NVIDI<PERSON>**: The underlying simulation platform
- **PyTorch**: Used for tensor operations and neural network implementations
- **NVIDIA Warp**: Optional GPU acceleration for soil simulation
- **RSL-RL**: Reinforcement learning framework for training agents

## 2. 3D Soil Model Architecture

The 3D soil model is the primary focus of this documentation, as it represents the most complex and realistic simulation of soil behavior in the system.

### 2.1 Core Classes

#### `Soil3D` (`soil_model_3d/soil_3d.py`)

The main class representing the 3D soil model, which inherits from `SoilModelBase`. It coordinates the interaction between various subcomponents:

- Manages the soil height field
- Handles soil-bucket interactions
- Coordinates force calculations
- Provides interface for spatial queries (BVH implementation planned for future)
- Updates visualization
- Provides interface for observations and measurements

#### `SoilHeight3D` (`soil_model_3d/soil_height_3d.py`)

Represents the 3D height field of the soil terrain:

- Manages the surface representation of the soil
- Provides methods for sampling height at arbitrary points
- Computes surface gradients (angles) at specified locations
- Supports terrain generation with RBF (Radial Basis Function) or slope-based methods
- Implements terrain modification operations (e.g., trench creation)

#### `BucketState3D` (`soil_model_3d/bucket_state_3d.py`)

Tracks the state of the excavator bucket:

- Stores bucket geometry and parameters
- Tracks bucket position, orientation, and velocity
- Calculates bucket fill ratio
- Determines which parts of the bucket are in contact with soil
- Computes bucket penetration depth

#### `SSP3D` (`soil_model_3d/ssp_3d.py`)

Implements the Soil Separation Plane (SSP) algorithm in 3D:

- Defines the separation plane that divides the soil volume
- Computes forces related to the SSP
- Determines failure surfaces in the soil

#### `SoilForces` (`soil_model_3d/soil_forces.py`)

Calculates forces exerted by the soil on the bucket:

- Computes penetration forces
- Calculates forces due to earth failure
- Determines forces from soil deadload
- Computes resultant forces and moments at the bucket's center of mass

#### `SoilParameters` (`soil_model_3d/soil_parameters.py`)

Manages soil physical properties:

- Density
- Cohesion
- Internal friction angle
- External friction coefficient
- Adhesion
- Dilatancy angle

### 2.2 BVH Integration

The 3D soil model currently uses direct grid queries for spatial operations. A Bounding Volume Hierarchy (BVH) implementation is planned for future optimization:

**Future BVH Implementation (Planned):**
1. **Initialization**: `_initialize_bvh()` in `Soil3D` would create the BVH structure
2. **Updates**: `_update_bvh()` would maintain the BVH structure as soil is deformed
3. **Queries**:
   - `query_ray_bvh()`: Would perform ray casting against the soil surface
   - `query_aabb_bvh()`: Would check for collisions with axis-aligned bounding boxes

**Current Implementation:**
- Direct grid-based spatial queries
- Sufficient performance for current use cases
- Simpler implementation and debugging

### 2.3 Warp Integration

The model supports optional acceleration using NVIDIA Warp:

1. **Kernel Registration**: Custom kernels are defined for soil operations
2. **Conditional Pathways**: Code paths are selected based on Warp availability
3. **Performance Optimization**: Critical calculations are offloaded to GPU when Warp is available

## 3. Soil Physics Model

### 3.1 Soil Representation

The soil is represented as a height field grid with the following properties:

- Resolution: Defined by configuration parameters
- Bounds: Specified by x_min, x_max, y_min, y_max
- Height Range: Constrained by z_min and upper_limit

### 3.2 Force Calculation

The soil-bucket interaction forces are calculated in several components:

#### 3.2.1 Penetration Forces

Forces arising from the bucket penetrating the soil:

1. **Normal Forces**: Perpendicular to the bucket surface
2. **Tangential Forces**: Along the bucket surface due to friction

#### 3.2.2 Earth Failure Forces

Forces due to earth failure mechanics, calculated via the SSP:

1. **Failure Surface Definition**: Based on Mohr-Coulomb failure criterion
2. **Force Integration**: Over the failure surface
3. **Direction Computation**: Based on soil parameters and geometry

#### 3.2.3 Deadload Forces

Forces due to the weight of soil in the bucket:

1. **Volume Calculation**: Based on bucket fill state
2. **Weight Force**: Product of volume, density, and gravity
3. **Application Point**: At the center of mass of soil in bucket

### 3.3 Soil Deformation

The soil terrain is deformed based on the excavator's actions:

1. **Bucket-Soil Intersection**: Determined via ray casting and BVH
2. **Material Removal**: Soil is removed from the height field where the bucket passes
3. **Material Accumulation**: Soil is added to the bucket based on excavation path
4. **Soil Slumping**: Natural angle of repose is maintained for stability

## 4. Training and Evaluation Framework

### 4.1 Training (`scripts/rsl_rl/train.py`)

The training script uses RSL-RL to train agents for the excavation task:

1. **Environment Setup**: Creates instances of ExcavationEnv
2. **Agent Configuration**: Sets up PPO (Proximal Policy Optimization) agent
3. **Training Loop**: Runs episodes, collects data, updates policy
4. **Evaluation**: Periodically evaluates agent performance
5. **Checkpointing**: Saves model weights at intervals
6. **Logging**: Records metrics (rewards, episode length, etc.)

### 4.2 Evaluation (`scripts/rsl_rl/play.py`)

The play script loads trained policies and evaluates them:

1. **Policy Loading**: Retrieves saved model weights
2. **Environment Recreation**: Sets up test environments
3. **Rollout**: Executes the policy on test environments
4. **Metrics Collection**: Records performance data
5. **Visualization**: Optionally records videos of agent performance

## 5. Key Functions and Algorithms

### 5.1 Soil Height Queries

```python
def get_height_at_point(self, x: torch.Tensor, y: torch.Tensor) -> torch.Tensor:
    """
    Gets the soil height at specified (x,y) coordinates.
    
    Args:
        x: x-coordinates [n_envs, n_points]
        y: y-coordinates [n_envs, n_points]
        
    Returns:
        heights: Soil heights at the specified points [n_envs, n_points]
    """
```

This function uses either an analytical solution (for simple terrain) or a grid-based interpolation to determine soil height at arbitrary points.

### 5.2 Spatial Queries (Current Implementation)

The current implementation uses direct grid-based queries for spatial operations:

```python
def get_soil_height_at_pos(self, x, y, env_ids=...):
    """
    Get soil height at specified positions using direct grid interpolation.

    Args:
        x: x positions to query [n_envs, n_points]
        y: y positions to query [n_envs, n_points]
        env_ids: Optional environment indices

    Returns:
        Tensor of soil heights at the specified positions
    """
```

**Future BVH Ray Queries (Planned):**
```python
def query_ray_bvh(self, start, direction, max_distance=100.0):
    """
    Would perform ray casting against the soil BVH (future implementation).

    Args:
        start: Origin points of rays [n_envs, n_rays, 3]
        direction: Direction vectors of rays [n_envs, n_rays, 3]
        max_distance: Maximum ray distance

    Returns:
        hit: Boolean indicating if ray hit soil [n_envs, n_rays]
        distance: Distance to hit point [n_envs, n_rays]
        normal: Surface normal at hit point [n_envs, n_rays, 3]
    """
```

This function is critical for determining the intersection between the bucket and soil surface.

### 5.3 Bucket Fill Calculation

```python
def _update_filling(self, idxs=None):
    """
    Updates the bucket fill state based on soil interaction.
    
    Args:
        idxs: Optional indices of environments to update
    """
```

This function tracks how much soil is in the bucket by:
1. Computing the volume of the bucket
2. Tracking soil that enters the bucket during excavation
3. Accounting for soil that might spill during movement

### 5.4 Force Integration

```python
def _update_fee(self):
    """
    Computes earth failure forces.
    """
```

This function calculates the forces due to earth failure by:
1. Determining the failure surface using the SSP
2. Integrating forces over the failure surface
3. Resolving forces into components acting on the bucket

## 6. Visualization

The soil model includes extensive visualization capabilities:

1. **Soil Surface**: Rendered as a dynamic mesh
2. **Bucket-Soil Interaction**: Highlighted during contact
3. **Force Vectors**: Displayed during simulation
4. **SSP Visualization**: Shows the soil separation plane
5. **BVH Debug View**: Optional visualization of the BVH structure

## 7. Performance Considerations

The 3D soil model is computationally intensive, with several optimizations:

1. **Vectorized Operations**: Uses PyTorch for parallel processing across environments
2. **GPU Acceleration**: Optional NVIDIA Warp integration for performance
3. **Adaptive Resolution**: Configurable grid density for performance vs. accuracy tradeoff
4. **BVH Optimization**: Efficient spatial queries through hierarchical structure
5. **Incremental Updates**: Only affected regions are updated each step

## 8. Future Development: Particle-Based Excavation Simulation

The next phase of development focuses on advancing the soil simulation to a particle-based model for more realistic excavation scenarios. Current soil models represent significant simplifications of real-world soil behavior, particularly in terms of soil inhomogeneities and displacement dynamics. The future implementation will address these limitations through several key innovations:

### 8.1 Limitations of Current Approaches

Current simulation approaches face several challenges:
- Simplified soil models that only compute forces at shovel edges
- Heavy reliance on domain randomization for sim-to-real transfer
- Inability to simulate soil inhomogeneities and displacement at sufficient speed
- Limited representation of soil flow dynamics during excavation
- Overly smooth terrain representation when using only RBF profiles

### 8.2 Cellular Automata (CA) Slope Model

The first phase of enhancement introduces a Cellular Automata slope model to update excavation geometry dynamically:

1. **Dynamic Mesh Generation**: Creating a mesh from initial geometry (e.g., meshgrid) that can be updated in real-time
2. **Real-Time Raycasting**: Implementing raycasting against the mesh for RL observations
   - Grid pattern (top-down) deployment in front of the excavator
   - Coverage spanning 4-8 meters (shovel width)
3. **Trench Formation**: Enabling consecutive digs with realistic trench development
4. **Soil Slumping**: Modeling natural slope creation after material removal

### 8.3 Material Point Method (MPM) Soil Simulation

The second phase integrates a particle-based soil model using the Material Point Method:

1. **High-Fidelity Particle Representation**: Modeling soil as discrete particles
2. **GPU Acceleration via Warp**: Leveraging NVIDIA Warp for performance optimization
3. **Realistic Soil Mechanics**:
   - Soil displacement and flow dynamics
   - Variable soil properties (density, cohesion, friction)
   - Inhomogeneities (rocks, moisture variations)
4. **Soil-Bucket Interaction**: Detailed simulation of soil behavior during bucket penetration and lifting

### 8.4 Reinforcement Learning Integration

The enhanced simulation environment will enable more sophisticated RL agent training:

1. **Progressive Training Approach**:
   - Initial training in CA-based environment
   - Advanced training in MPM-based environment
   - Curriculum learning for handling increasingly complex scenarios
2. **Observation Enrichment**: Providing agents with more realistic sensor data
3. **Performance Evaluation**: Comparing agent performance across different soil modeling approaches
4. **Transfer Learning**: Facilitating better sim-to-real transfer with reduced domain randomization

### 8.5 Technical Implementation

The implementation will follow these work packages:

1. **Dynamic Terrain Representation**:
   - Implement the Cellular Automata-based slope model
   - Enable mesh generation and real-time raycasting
   - Create dynamic update mechanisms for excavation geometry
   
2. **Particle-Based Soil Model**:
   - Develop the MPM simulation using Warp
   - Optimize for performance across multiple environments
   - Implement realistic bucket-soil interactions
   
3. **RL Agent Training**:
   - Train agents in both CA and MPM environments
   - Develop metrics to evaluate digging performance
   - Optimize agent behavior for real-world excavation challenges

By combining these advanced soil modeling techniques with reinforcement learning, the project aims to create a more realistic simulation environment that can train agents capable of handling the complexities of real-world excavation tasks.